import dotenv from 'dotenv';
import { z } from 'zod';

// Load environment variables
dotenv.config();

// Define environment schema for validation
const envSchema = z.object({
  // Server Configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3001'),

  // Database Configuration
  DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),

  // JWT Configuration
  JWT_SECRET: z.string().min(32, 'JWT_SECRET must be at least 32 characters'),
  JWT_REFRESH_SECRET: z.string().min(32, 'JWT_REFRESH_SECRET must be at least 32 characters'),
  JWT_EXPIRES_IN: z.string().default('15m'),
  JWT_REFRESH_EXPIRES_IN: z.string().default('7d'),

  // Redis Configuration
  REDIS_URL: z.string().default('redis://localhost:6379'),
  REDIS_PASSWORD: z.string().optional(),

  // File Upload Configuration
  UPLOAD_DIR: z.string().default('./uploads'),
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB

  // Email Configuration
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  FROM_EMAIL: z.string().optional(),

  // Scheduling Engine Configuration
  MAX_GENERATION_TIME: z.string().transform(Number).default('1800'), // 30 minutes
  DEFAULT_TIME_SLOTS: z.string().transform(Number).default('26'),
  WORKING_DAY_START: z.string().default('09:30'),
  WORKING_DAY_END: z.string().default('16:00'),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: z.string().transform(Number).default('900000'), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(Number).default('100'),

  // Logging Configuration
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'http', 'debug']).default('info'),
  LOG_FILE: z.string().default('./logs/app.log'),

  // CORS Configuration
  ALLOWED_ORIGINS: z.string().default('http://localhost:3000,http://localhost:5173'),

  // Security Configuration
  BCRYPT_ROUNDS: z.string().transform(Number).default('12'),
  SESSION_SECRET: z.string().min(32, 'SESSION_SECRET must be at least 32 characters'),

  // Development/Testing
  ENABLE_SWAGGER: z.string().transform(Boolean).default('true'),
  ENABLE_PRISMA_STUDIO: z.string().transform(Boolean).default('true'),
});

// Validate environment variables
const parseEnv = () => {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Environment validation failed:\n${missingVars.join('\n')}`);
    }
    throw error;
  }
};

// Export validated environment configuration
export const env = parseEnv();

// Helper functions for common environment checks
export const isDevelopment = () => env.NODE_ENV === 'development';
export const isProduction = () => env.NODE_ENV === 'production';
export const isTest = () => env.NODE_ENV === 'test';

// Time slot utilities
export const timeSlotUtils = {
  totalSlots: env.DEFAULT_TIME_SLOTS,
  workingDayStart: env.WORKING_DAY_START,
  workingDayEnd: env.WORKING_DAY_END,
  slotDurationMinutes: 15,

  // Convert slot number to time string
  slotToTime: (slot: number): string => {
    const [startHour, startMinute] = env.WORKING_DAY_START.split(':').map(Number);
    const totalMinutes = startHour * 60 + startMinute + (slot - 1) * 15;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  },

  // Convert time string to slot number
  timeToSlot: (time: string): number => {
    const [hour, minute] = time.split(':').map(Number);
    const [startHour, startMinute] = env.WORKING_DAY_START.split(':').map(Number);
    const timeMinutes = hour * 60 + minute;
    const startMinutes = startHour * 60 + startMinute;
    return Math.floor((timeMinutes - startMinutes) / 15) + 1;
  },

  // Get all time slots with their time ranges
  getAllTimeSlots: () => {
    const slots = [];
    for (let i = 1; i <= env.DEFAULT_TIME_SLOTS; i++) {
      const startTime = timeSlotUtils.slotToTime(i);
      const endTime = timeSlotUtils.slotToTime(i + 1);
      slots.push({
        slot: i,
        startTime,
        endTime,
        label: `${startTime}-${endTime}`,
      });
    }
    return slots;
  },
};

export default env;