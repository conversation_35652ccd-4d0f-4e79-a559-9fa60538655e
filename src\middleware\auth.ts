import { NextFunction, Response } from 'express';
import jwt from 'jsonwebtoken';
import { UserRole } from '@prisma/client';
import { AuthenticatedRequest, JWTPayload } from '../types/api';
import { sendUnauthorized, sendForbidden } from '../utils/response';
import { env } from '../config/env';
import { logger } from '../config/logger';
import prisma from '../config/database';

/**
 * Verify JWT token and attach user to request
 */
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return sendUnauthorized(res, 'Access token required');
    }

    // Verify the token
    const decoded = jwt.verify(token, env.JWT_SECRET) as JWTPayload;

    // Fetch fresh user data from database
    const user = await prisma.user.findUnique({
      where: { userID: decoded.userID },
      select: {
        userID: true,
        username: true,
        email: true,
        role: true,
        departmentID: true,
        instructorID: true,
        isActive: true,
      },
    });

    if (!user || !user.isActive) {
      return sendUnauthorized(res, 'Invalid or inactive user');
    }

    // Attach user to request
    req.user = {
      userID: user.userID,
      username: user.username,
      email: user.email,
      role: user.role,
      departmentID: user.departmentID || undefined,
      instructorID: user.instructorID || undefined,
    };

    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      logger.warn('Invalid JWT token', { error: error.message });
      return sendUnauthorized(res, 'Invalid access token');
    }

    if (error instanceof jwt.TokenExpiredError) {
      logger.warn('Expired JWT token', { error: error.message });
      return sendUnauthorized(res, 'Access token expired');
    }

    logger.error('Authentication error', { error });
    return sendUnauthorized(res, 'Authentication failed');
  }
};

/**
 * Check if user has required role(s)
 */
export const requireRole = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return sendUnauthorized(res, 'Authentication required');
    }

    if (!roles.includes(req.user.role)) {
      logger.warn('Insufficient permissions', {
        userID: req.user.userID,
        userRole: req.user.role,
        requiredRoles: roles,
      });
      return sendForbidden(res, 'Insufficient permissions');
    }

    next();
  };
};

/**
 * Check if user is admin
 */
export const requireAdmin = requireRole(UserRole.ADMIN);

/**
 * Check if user is admin or scheduler
 */
export const requireScheduler = requireRole(UserRole.ADMIN, UserRole.SCHEDULER);

/**
 * Check if user is admin, scheduler, or department coordinator
 */
export const requireCoordinator = requireRole(
  UserRole.ADMIN,
  UserRole.SCHEDULER,
  UserRole.DEPARTMENT_COORDINATOR
);

/**
 * Check if user can access department data
 */
export const requireDepartmentAccess = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    return sendUnauthorized(res, 'Authentication required');
  }

  const { role, departmentID } = req.user;
  const requestedDepartmentID = req.params.departmentId || req.body.departmentID;

  // Admins and schedulers can access all departments
  if (role === UserRole.ADMIN || role === UserRole.SCHEDULER) {
    return next();
  }

  // Department coordinators and instructors can only access their own department
  if (
    (role === UserRole.DEPARTMENT_COORDINATOR || role === UserRole.INSTRUCTOR) &&
    departmentID === requestedDepartmentID
  ) {
    return next();
  }

  logger.warn('Department access denied', {
    userID: req.user.userID,
    userRole: role,
    userDepartmentID: departmentID,
    requestedDepartmentID,
  });

  return sendForbidden(res, 'Access denied to this department');
};

/**
 * Check if user can access instructor data
 */
export const requireInstructorAccess = (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user) {
    return sendUnauthorized(res, 'Authentication required');
  }

  const { role, instructorID } = req.user;
  const requestedInstructorID = req.params.instructorId || req.body.instructorID;

  // Admins and schedulers can access all instructors
  if (role === UserRole.ADMIN || role === UserRole.SCHEDULER) {
    return next();
  }

  // Instructors can only access their own data
  if (role === UserRole.INSTRUCTOR && instructorID === requestedInstructorID) {
    return next();
  }

  logger.warn('Instructor access denied', {
    userID: req.user.userID,
    userRole: role,
    userInstructorID: instructorID,
    requestedInstructorID,
  });

  return sendForbidden(res, 'Access denied to this instructor data');
};

/**
 * Optional authentication - attach user if token is valid, but don't require it
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next(); // No token, continue without user
    }

    const decoded = jwt.verify(token, env.JWT_SECRET) as JWTPayload;
    const user = await prisma.user.findUnique({
      where: { userID: decoded.userID },
      select: {
        userID: true,
        username: true,
        email: true,
        role: true,
        departmentID: true,
        instructorID: true,
        isActive: true,
      },
    });

    if (user && user.isActive) {
      req.user = {
        userID: user.userID,
        username: user.username,
        email: user.email,
        role: user.role,
        departmentID: user.departmentID || undefined,
        instructorID: user.instructorID || undefined,
      };
    }

    next();
  } catch (error) {
    // Invalid token, but continue without user
    next();
  }
};