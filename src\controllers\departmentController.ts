import { Response } from 'express';
import { z } from 'zod';
import { AuthenticatedRequest, CreateDepartmentDTO, UpdateDepartmentDTO } from '../types/api';
import { DepartmentService } from '../services/departmentService';
import {
  sendSuccess,
  sendCreated,
  sendNotFound,
  sendBadRequest,
  sendUnprocessableEntity,
  sendInternalServerError,
  calculatePagination,
  parsePaginationQuery,
  asyncHandler
} from '../utils/response';
import { logger } from '../config/logger';

// Validation schemas
const createDepartmentSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  code: z.string().max(10, 'Code too long').optional(),
});

const updateDepartmentSchema = createDepartmentSchema.partial();

/**
 * Department Controller - Handles HTTP requests for department management
 */
export class DepartmentController {
  /**
   * GET /api/departments
   * Get all departments with optional filtering
   */
  static getAllDepartments = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { page, limit, skip } = parsePaginationQuery(req.query);
      const search = req.query.search as string;

      const { departments, total } = await DepartmentService.getAllDepartments({
        page,
        limit,
        search,
      });

      const pagination = calculatePagination(total, page, limit);

      sendSuccess(res, departments, 'Departments retrieved successfully', pagination);
    } catch (error) {
      logger.error('Error retrieving departments', { error });
      sendInternalServerError(res, 'Failed to retrieve departments');
    }
  });

  /**
   * GET /api/departments/:departmentId
   * Get department by ID
   */
  static getDepartmentById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { departmentId } = req.params;

      const department = await DepartmentService.getDepartmentById(departmentId);
      if (!department) {
        return sendNotFound(res, 'Department not found');
      }

      sendSuccess(res, department, 'Department retrieved successfully');
    } catch (error) {
      logger.error('Error retrieving department', { departmentId: req.params.departmentId, error });
      sendInternalServerError(res, 'Failed to retrieve department');
    }
  });

  /**
   * GET /api/departments/:departmentId/statistics
   * Get department statistics
   */
  static getDepartmentStatistics = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { departmentId } = req.params;

      const statistics = await DepartmentService.getDepartmentStatistics(departmentId);
      if (!statistics) {
        return sendNotFound(res, 'Department not found');
      }

      sendSuccess(res, statistics, 'Department statistics retrieved successfully');
    } catch (error) {
      logger.error('Error retrieving department statistics', { departmentId: req.params.departmentId, error });
      sendInternalServerError(res, 'Failed to retrieve department statistics');
    }
  });

  /**
   * GET /api/departments/dropdown
   * Get departments for dropdown (minimal data)
   */
  static getDepartmentsForDropdown = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const departments = await DepartmentService.getDepartmentsForDropdown();
      sendSuccess(res, departments, 'Departments for dropdown retrieved successfully');
    } catch (error) {
      logger.error('Error retrieving departments for dropdown', { error });
      sendInternalServerError(res, 'Failed to retrieve departments for dropdown');
    }
  });

  /**
   * POST /api/departments
   * Create new department
   */
  static createDepartment = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Validate request body
      const validationResult = createDepartmentSchema.safeParse(req.body);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return sendUnprocessableEntity(res, 'Validation failed', errors);
      }

      const data: CreateDepartmentDTO = validationResult.data;

      // Check if name is unique
      const isNameUnique = await DepartmentService.isDepartmentNameUnique(data.name);
      if (!isNameUnique) {
        return sendBadRequest(res, 'Department name already exists');
      }

      // Check if code is unique (if provided)
      if (data.code) {
        const isCodeUnique = await DepartmentService.isDepartmentCodeUnique(data.code);
        if (!isCodeUnique) {
          return sendBadRequest(res, 'Department code already exists');
        }
      }

      const department = await DepartmentService.createDepartment(data);
      sendCreated(res, department, 'Department created successfully');
    } catch (error) {
      logger.error('Error creating department', { data: req.body, error });
      sendInternalServerError(res, 'Failed to create department');
    }
  });

  /**
   * PUT /api/departments/:departmentId
   * Update department
   */
  static updateDepartment = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { departmentId } = req.params;

      // Validate request body
      const validationResult = updateDepartmentSchema.safeParse(req.body);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return sendUnprocessableEntity(res, 'Validation failed', errors);
      }

      const data: UpdateDepartmentDTO = validationResult.data;

      // Check if name is unique (excluding current department)
      if (data.name) {
        const isNameUnique = await DepartmentService.isDepartmentNameUnique(data.name, departmentId);
        if (!isNameUnique) {
          return sendBadRequest(res, 'Department name already exists');
        }
      }

      // Check if code is unique (excluding current department)
      if (data.code) {
        const isCodeUnique = await DepartmentService.isDepartmentCodeUnique(data.code, departmentId);
        if (!isCodeUnique) {
          return sendBadRequest(res, 'Department code already exists');
        }
      }

      const department = await DepartmentService.updateDepartment(departmentId, data);
      if (!department) {
        return sendNotFound(res, 'Department not found');
      }

      sendSuccess(res, department, 'Department updated successfully');
    } catch (error) {
      logger.error('Error updating department', { departmentId: req.params.departmentId, data: req.body, error });
      sendInternalServerError(res, 'Failed to update department');
    }
  });

  /**
   * DELETE /api/departments/:departmentId
   * Delete department
   */
  static deleteDepartment = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { departmentId } = req.params;

      const success = await DepartmentService.deleteDepartment(departmentId);
      if (!success) {
        return sendNotFound(res, 'Department not found');
      }

      sendSuccess(res, null, 'Department deleted successfully');
    } catch (error) {
      if (error instanceof Error && error.message.includes('associated')) {
        return sendBadRequest(res, error.message);
      }

      logger.error('Error deleting department', { departmentId: req.params.departmentId, error });
      sendInternalServerError(res, 'Failed to delete department');
    }
  });
}