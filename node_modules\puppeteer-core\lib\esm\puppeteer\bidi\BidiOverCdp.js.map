{"version": 3, "file": "BidiOverCdp.js", "sourceRoot": "", "sources": ["../../../../src/bidi/BidiOverCdp.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,UAAU,MAAM,gDAAgD,CAAC;AAM7E,OAAO,EAAC,KAAK,EAAC,MAAM,oBAAoB,CAAC;AACzC,OAAO,EAAC,gBAAgB,EAAC,MAAM,qBAAqB,CAAC;AAGrD,OAAO,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AAE/C,MAAM,gBAAgB,GAAG,CAAC,MAAc,EAAE,GAAG,IAAe,EAAQ,EAAE;IACpE,KAAK,CAAC,QAAQ,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;AAChC,CAAC,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CACtC,GAAkB;AAClB,uEAAuE;AACvE,sDAAsD;AACtD,OAAuC;IAEvC,MAAM,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC;IAC1C,MAAM,oBAAoB,GAAG,IAAI,oBAAoB,CAAC,GAAG,CAAC,CAAC;IAC3D,MAAM,aAAa,GAAG;QACpB,IAAI,CAAC,OAAe;YAClB,4EAA4E;YAC5E,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QACjD,CAAC;QACD,KAAK;YACH,UAAU,CAAC,KAAK,EAAE,CAAC;YACnB,oBAAoB,CAAC,KAAK,EAAE,CAAC;YAC7B,GAAG,CAAC,OAAO,EAAE,CAAC;QAChB,CAAC;QACD,SAAS,CAAC,QAAgB;YACxB,8CAA8C;QAChD,CAAC;KACF,CAAC;IACF,aAAa,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,OAAe,EAAE,EAAE;QACnD,yDAAyD;QACzD,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;IACnD,CAAC,CAAC,CAAC;IACH,MAAM,kBAAkB,GAAG,IAAI,cAAc,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,aAAa,CAAC,CAAC;IACxE,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,UAAU,CAAC,cAAc,CAC3D,aAAa,EACb,oBAAoB;IACpB,qDAAqD;IACrD,oBAAoB,CAAC,aAAa,EAAE,EACpC,EAAE,EACF,OAAO,EACP,SAAS,EACT,gBAAgB,CACjB,CAAC;IACF,OAAO,kBAAkB,CAAC;AAC5B,CAAC;AAED;;;GAGG;AACH,MAAM,oBAAoB;IACxB,IAAI,CAAgB;IACpB,SAAS,GAAG,IAAI,GAAG,EAA4C,CAAC;IAChE,qBAAqB,CAAkC;IAEvD,YAAY,GAAkB;QAC5B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;QAChB,IAAI,CAAC,qBAAqB,GAAG,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC;IACzD,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACpC,CAAC;IAED,YAAY,CAAC,EAAU;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,+BAA+B,EAAE,EAAE,CAAC,CAAC;QACvD,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,IAAI,gBAAgB,CAClC,OAAO,EACP,EAAE,EACF,IAAI,CAAC,qBAAqB,CAC3B,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YACrC,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;IACtC,CAAC;IAED,KAAK;QACH,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAC;QACnC,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,EAAE,CAAC;YAC9C,OAAO,CAAC,KAAK,EAAE,CAAC;QAClB,CAAC;IACH,CAAC;CACF;AAED;;;;;GAKG;AACH,MAAM,gBACJ,SAAQ,UAAU,CAAC,YAAuB;IAG1C,OAAO,GAAG,KAAK,CAAC;IAChB,OAAO,CAAI;IACX,SAAS,GAAuB,SAAS,CAAC;IAC1C,cAAc,CAAwB;IAEtC,YACE,MAAS,EACT,SAAkB,EAClB,aAAoC;QAEpC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,IAAI,CAAC,eAA+B,CAAC,CAAC;IAC7D,CAAC;IAED,aAAa;QACX,OAAO,IAAI,CAAC,cAAe,CAAC;IAC9B,CAAC;IAED,eAAe,GAAG,CAChB,MAAS,EACT,KAAmB,EACnB,EAAE;QACF,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IAC3B,CAAC,CAAC;IAEF,KAAK,CAAC,WAAW,CACf,MAAS,EACT,GAAG,MAAiD;QAEpD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QACD,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,MAAM,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACjB,OAAO;YACT,CAAC;YACD,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK;QACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,eAA+B,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,YAAY,CAAC,KAAc;QACzB,OAAO,KAAK,YAAY,gBAAgB,CAAC;IAC3C,CAAC;CACF;AAED;;;;GAIG;AACH,MAAM,aACJ,SAAQ,UAAU,CAAC,YAEjB;IAGF,UAAU,GACR,KAAK,EAAE,EAA6B,EAAiB,EAAE;QACrD,OAAO;IACT,CAAC,CAAC;IAEJ,WAAW,CAAC,OAAkC;QAC5C,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,YAAY,CACV,SAAuE;QAEvE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;IAC9B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAkC;QAClD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,KAAK;QACH,IAAI,CAAC,UAAU,GAAG,KAAK,EAAE,EAA6B,EAAiB,EAAE;YACvE,OAAO;QACT,CAAC,CAAC;IACJ,CAAC;CACF"}