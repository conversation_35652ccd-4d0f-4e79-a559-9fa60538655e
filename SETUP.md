# University Schedule Assistant - Setup Guide

## Phase 1: Database Design & Implementation - COMPLETED ✅

This guide will help you set up the University Schedule Assistant system for development and production.

## Prerequisites

- Node.js 18+ and npm 9+
- PostgreSQL 15+
- Redis 6+
- Docker and Docker Compose (optional, for containerized setup)

## Quick Start with Docker (Recommended)

### 1. <PERSON><PERSON> and Setup
```bash
git clone <repository-url>
cd Schedule_assisstnant
cp .env.example .env
```

### 2. Configure Environment
Edit `.env` file with your settings:
```bash
# Update database credentials if needed
DATABASE_URL="postgresql://schedule_user:schedule_password@localhost:5432/schedule_assistant?schema=public"

# Generate secure secrets
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
```

### 3. Start Services
```bash
# Start database and Redis only
docker-compose up postgres redis -d

# Or start all services including API
docker-compose up -d
```

### 4. Initialize Database
```bash
# Install dependencies
npm install

# Generate Prisma client
npm run db:generate

# Run database migrations
npm run db:migrate

# Seed with sample data
npm run db:seed
```

### 5. Start Development Server
```bash
npm run dev
```

The API will be available at `http://localhost:3001`

## Manual Setup (Without Docker)

### 1. Database Setup
```bash
# Create PostgreSQL database
createdb schedule_assistant

# Run the schema
psql -d schedule_assistant -f database_schema.sql
```

### 2. Redis Setup
```bash
# Start Redis server
redis-server
```

### 3. Application Setup
```bash
# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env with your database and Redis URLs

# Generate Prisma client
npm run db:generate

# Run migrations
npm run db:migrate

# Start development server
npm run dev
```

## Database Schema Overview

The Phase 1 implementation includes:

### Core Tables
- **Semesters**: Academic periods
- **Departments**: Organizational units
- **Rooms**: Physical spaces with capacity and type
- **Instructors**: Faculty with availability
- **InstructorAvailability**: Normalized availability data
- **StudentLevels**: Academic levels (e.g., "First Year Pharmacy")
- **StudentGroups**: Hierarchical student groupings
- **Courses**: Course definitions
- **Lessons**: Concrete scheduling instances (the "problem definition")

### Operational Tables
- **ScheduledLessons**: Final generated schedule
- **Substitutions**: Daily substitution management
- **PublishedSchedules**: Stable schedule snapshots
- **SchedulingConstraints**: Flexible constraint storage
- **GenerationSessions**: Workflow tracking
- **Users**: Basic user management

### Key Features Implemented
- ✅ Complete database schema with proper relationships
- ✅ Prisma ORM integration with type safety
- ✅ Normalized instructor availability storage
- ✅ Flexible constraint system using JSONB
- ✅ Workflow tracking for schedule generation
- ✅ User management with role-based access
- ✅ Docker containerization for easy deployment
- ✅ Sample data for development

## Time Slot Structure

The system uses a 26-slot time grid:
- **Working Day**: 9:30 AM - 4:00 PM (6.5 hours)
- **Slot Duration**: 15 minutes each
- **Slot Numbers**: 1-26 (mapped to specific times)

Example:
- Slot 1: 09:30-09:45
- Slot 5: 10:30-10:45
- Slot 26: 15:45-16:00

## Available Scripts

```bash
# Development
npm run dev              # Start development server with hot reload
npm run build           # Build for production
npm run start           # Start production server

# Database
npm run db:generate     # Generate Prisma client
npm run db:migrate      # Run database migrations
npm run db:seed         # Seed database with sample data
npm run db:studio       # Open Prisma Studio
npm run db:reset        # Reset database (development only)

# Testing
npm run test            # Run tests
npm run test:watch      # Run tests in watch mode
npm run test:coverage   # Run tests with coverage

# Code Quality
npm run lint            # Run ESLint
npm run lint:fix        # Fix ESLint issues
npm run format          # Format code with Prettier
```

## API Endpoints (Phase 1 Ready)

The database is ready to support all planned API endpoints:

### Core Data Management
- `GET/POST/PUT/DELETE /api/semesters`
- `GET/POST/PUT/DELETE /api/departments`
- `GET/POST/PUT/DELETE /api/rooms`
- `GET/POST/PUT/DELETE /api/instructors`
- `GET/POST/PUT/DELETE /api/student-levels`
- `GET/POST/PUT/DELETE /api/student-groups`
- `GET/POST/PUT/DELETE /api/courses`
- `GET/POST/PUT/DELETE /api/lessons`

### Workflow Management
- `POST /api/generation-sessions`
- `POST /api/generation-sessions/{id}/generate-manifest`
- `POST /api/generation-sessions/{id}/upload-manifest`
- `POST /api/generation-sessions/{id}/start-generation`

### Schedule Management
- `GET/POST/PUT/DELETE /api/scheduled-lessons`
- `POST /api/scheduled-lessons/move`
- `POST /api/scheduled-lessons/swap`

### Substitution Management
- `GET/POST/PUT/DELETE /api/substitutions`
- `GET /api/substitutions/{id}/suggestions`
- `POST /api/substitutions/daily-bulletin`

## Next Steps - Phase 2: Backend Development

With Phase 1 complete, you can now proceed to Phase 2:

1. **Core API Development** - Implement RESTful CRUD endpoints
2. **Bulk Upload Module** - Excel/CSV file processing
3. **Scheduling Engine** - Constraint satisfaction algorithm
4. **Workflow Logic** - Manifest generation and validation
5. **Authentication & Authorization** - JWT-based security

## Troubleshooting

### Database Connection Issues
```bash
# Check if PostgreSQL is running
pg_isready -h localhost -p 5432

# Check database exists
psql -l | grep schedule_assistant
```

### Redis Connection Issues
```bash
# Check if Redis is running
redis-cli ping
```

### Prisma Issues
```bash
# Reset Prisma client
rm -rf node_modules/.prisma
npm run db:generate
```

### Docker Issues
```bash
# Check container status
docker-compose ps

# View logs
docker-compose logs api
docker-compose logs postgres
```

## Development Tips

1. **Use Prisma Studio** for database visualization: `npm run db:studio`
2. **Check logs** for debugging: `docker-compose logs -f api`
3. **Reset database** when schema changes: `npm run db:reset`
4. **Use sample data** for testing: The schema includes sample data inserts

## Production Deployment

For production deployment:

1. Use environment-specific `.env` files
2. Set up proper SSL certificates
3. Configure Nginx reverse proxy
4. Set up monitoring and logging
5. Use managed database services (AWS RDS, etc.)
6. Implement proper backup strategies

## Support

- Check the [API Design](./api_design.md) for endpoint specifications
- Review [System Architecture](./system_architecture.md) for technical details
- Follow [Workflow Process](./workflow_process.md) for user journey understanding

---

**Phase 1 Status: ✅ COMPLETE**

The database foundation is ready for Phase 2 backend development!