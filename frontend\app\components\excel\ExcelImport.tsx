import { useState, useRef } from "react";
import { Upload, FileSpreadsheet, AlertCircle, CheckCircle, Download } from "lucide-react";
import { Button } from "~/components/ui/button";

interface ExcelImportProps {
  type: 'departments' | 'rooms' | 'instructors' | 'courses';
  semesterID?: string;
  onImportComplete?: (result: any) => void;
}

interface ImportResult {
  success: boolean;
  importedCount: number;
  errors: string[];
  warnings: string[];
  message: string;
}

export function ExcelImport({ type, semesterID, onImportComplete }: ExcelImportProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    if (!file) return;

    // Validate file type
    if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
      setImportResult({
        success: false,
        importedCount: 0,
        errors: ['Please select a valid Excel file (.xlsx or .xls)'],
        warnings: [],
        message: 'Invalid file type'
      });
      return;
    }

    setIsUploading(true);
    setImportResult(null);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      if (semesterID) {
        formData.append('semesterID', semesterID);
      }

      const response = await fetch('/api/excel/import', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        setImportResult({
          success: result.success,
          importedCount: result.data.importedCount,
          errors: result.data.errors,
          warnings: result.data.warnings,
          message: result.message
        });

        if (result.success && onImportComplete) {
          onImportComplete(result);
        }
      } else {
        setImportResult({
          success: false,
          importedCount: 0,
          errors: [result.message || 'Import failed'],
          warnings: [],
          message: 'Import failed'
        });
      }
    } catch (error) {
      setImportResult({
        success: false,
        importedCount: 0,
        errors: ['Network error occurred during import'],
        warnings: [],
        message: 'Import failed'
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const downloadTemplate = async () => {
    try {
      const params = new URLSearchParams({
        templateType: type,
        ...(semesterID && { semesterID })
      });

      const response = await fetch(`/api/excel/export?type=template&${params}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${type}_template.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading template:', error);
    }
  };

  const getTypeDisplayName = () => {
    switch (type) {
      case 'departments': return 'Departments';
      case 'rooms': return 'Rooms';
      case 'instructors': return 'Instructors';
      case 'courses': return 'Courses';
      default: return 'Data';
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Import {getTypeDisplayName()}</h3>
        <Button variant="outline" onClick={downloadTemplate}>
          <Download className="mr-2 h-4 w-4" />
          Download Template
        </Button>
      </div>

      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive
            ? 'border-primary bg-primary/5'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".xlsx,.xls"
          onChange={handleFileInputChange}
          className="hidden"
        />

        <FileSpreadsheet className="mx-auto h-12 w-12 text-gray-400 mb-4" />

        <div className="space-y-2">
          <p className="text-lg font-medium">
            Drop your Excel file here, or{' '}
            <button
              onClick={() => fileInputRef.current?.click()}
              className="text-primary hover:underline"
            >
              browse
            </button>
          </p>
          <p className="text-sm text-gray-500">
            Supports .xlsx and .xls files up to 10MB
          </p>
        </div>

        {isUploading && (
          <div className="mt-4">
            <div className="inline-flex items-center space-x-2">
              <Upload className="h-4 w-4 animate-spin" />
              <span>Uploading and processing...</span>
            </div>
          </div>
        )}
      </div>

      {/* Import Results */}
      {importResult && (
        <div className={`rounded-lg border p-4 ${
          importResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
        }`}>
          <div className="flex items-start space-x-3">
            {importResult.success ? (
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
            )}

            <div className="flex-1">
              <h4 className={`font-medium ${
                importResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {importResult.message}
              </h4>

              {importResult.success && importResult.importedCount > 0 && (
                <p className="text-sm text-green-700 mt-1">
                  Successfully imported {importResult.importedCount} {getTypeDisplayName().toLowerCase()}
                </p>
              )}

              {/* Warnings */}
              {importResult.warnings.length > 0 && (
                <div className="mt-3">
                  <h5 className="text-sm font-medium text-yellow-800">Warnings:</h5>
                  <ul className="mt-1 text-sm text-yellow-700 space-y-1">
                    {importResult.warnings.map((warning, index) => (
                      <li key={index} className="flex items-start space-x-1">
                        <span className="text-yellow-600">•</span>
                        <span>{warning}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Errors */}
              {importResult.errors.length > 0 && (
                <div className="mt-3">
                  <h5 className="text-sm font-medium text-red-800">Errors:</h5>
                  <ul className="mt-1 text-sm text-red-700 space-y-1">
                    {importResult.errors.map((error, index) => (
                      <li key={index} className="flex items-start space-x-1">
                        <span className="text-red-600">•</span>
                        <span>{error}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
        <h4 className="font-medium text-blue-800 mb-2">Import Instructions</h4>
        <div className="text-sm text-blue-700 space-y-1">
          <p>1. Download the template file using the button above</p>
          <p>2. Fill in your data following the template format</p>
          <p>3. Save the file and upload it using the area above</p>
          <p>4. Review the import results and fix any errors if needed</p>
        </div>
      </div>
    </div>
  );
}