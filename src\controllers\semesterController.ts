import { Response } from 'express';
import { z } from 'zod';
import { AuthenticatedRequest, CreateSemesterDTO, UpdateSemesterDTO } from '../types/api';
import { SemesterService } from '../services/semesterService';
import {
  sendSuccess,
  sendCreated,
  sendNotFound,
  sendBadRequest,
  sendUnprocessableEntity,
  sendInternalServerError,
  calculatePagination,
  parsePaginationQuery,
  asyncHandler
} from '../utils/response';
import { logger } from '../config/logger';

// Validation schemas
const createSemesterSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format (YYYY-MM-DD)'),
  isActive: z.boolean().optional(),
});

const updateSemesterSchema = createSemesterSchema.partial();

/**
 * Semester Controller - Handles HTTP requests for semester management
 */
export class SemesterController {
  /**
   * GET /api/semesters
   * Get all semesters with optional filtering
   */
  static getAllSemesters = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { page, limit, skip } = parsePaginationQuery(req.query);
      const active = req.query.active === 'true' ? true : req.query.active === 'false' ? false : undefined;

      const { semesters, total } = await SemesterService.getAllSemesters({
        page,
        limit,
        active,
      });

      const pagination = calculatePagination(total, page, limit);

      sendSuccess(res, semesters, 'Semesters retrieved successfully', pagination);
    } catch (error) {
      logger.error('Error retrieving semesters', { error });
      sendInternalServerError(res, 'Failed to retrieve semesters');
    }
  });

  /**
   * GET /api/semesters/:semesterId
   * Get semester by ID
   */
  static getSemesterById = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { semesterId } = req.params;

      const semester = await SemesterService.getSemesterById(semesterId);
      if (!semester) {
        return sendNotFound(res, 'Semester not found');
      }

      sendSuccess(res, semester, 'Semester retrieved successfully');
    } catch (error) {
      logger.error('Error retrieving semester', { semesterId: req.params.semesterId, error });
      sendInternalServerError(res, 'Failed to retrieve semester');
    }
  });

  /**
   * GET /api/semesters/active
   * Get active semester
   */
  static getActiveSemester = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const semester = await SemesterService.getActiveSemester();
      if (!semester) {
        return sendNotFound(res, 'No active semester found');
      }

      sendSuccess(res, semester, 'Active semester retrieved successfully');
    } catch (error) {
      logger.error('Error retrieving active semester', { error });
      sendInternalServerError(res, 'Failed to retrieve active semester');
    }
  });

  /**
   * POST /api/semesters
   * Create new semester
   */
  static createSemester = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      // Validate request body
      const validationResult = createSemesterSchema.safeParse(req.body);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return sendUnprocessableEntity(res, 'Validation failed', errors);
      }

      const data: CreateSemesterDTO = validationResult.data;

      // Validate semester dates
      const dateErrors = SemesterService.validateSemesterDates(data.startDate, data.endDate);
      if (dateErrors.length > 0) {
        return sendBadRequest(res, 'Invalid semester dates', dateErrors);
      }

      // Check if name is unique
      const isNameUnique = await SemesterService.isSemesterNameUnique(data.name);
      if (!isNameUnique) {
        return sendBadRequest(res, 'Semester name already exists');
      }

      const semester = await SemesterService.createSemester(data);
      sendCreated(res, semester, 'Semester created successfully');
    } catch (error) {
      logger.error('Error creating semester', { data: req.body, error });
      sendInternalServerError(res, 'Failed to create semester');
    }
  });

  /**
   * PUT /api/semesters/:semesterId
   * Update semester
   */
  static updateSemester = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { semesterId } = req.params;

      // Validate request body
      const validationResult = updateSemesterSchema.safeParse(req.body);
      if (!validationResult.success) {
        const errors = validationResult.error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
        return sendUnprocessableEntity(res, 'Validation failed', errors);
      }

      const data: UpdateSemesterDTO = validationResult.data;

      // Validate semester dates if provided
      if (data.startDate && data.endDate) {
        const dateErrors = SemesterService.validateSemesterDates(data.startDate, data.endDate);
        if (dateErrors.length > 0) {
          return sendBadRequest(res, 'Invalid semester dates', dateErrors);
        }
      }

      // Check if name is unique (excluding current semester)
      if (data.name) {
        const isNameUnique = await SemesterService.isSemesterNameUnique(data.name, semesterId);
        if (!isNameUnique) {
          return sendBadRequest(res, 'Semester name already exists');
        }
      }

      const semester = await SemesterService.updateSemester(semesterId, data);
      if (!semester) {
        return sendNotFound(res, 'Semester not found');
      }

      sendSuccess(res, semester, 'Semester updated successfully');
    } catch (error) {
      logger.error('Error updating semester', { semesterId: req.params.semesterId, data: req.body, error });
      sendInternalServerError(res, 'Failed to update semester');
    }
  });

  /**
   * DELETE /api/semesters/:semesterId
   * Delete semester
   */
  static deleteSemester = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { semesterId } = req.params;

      const success = await SemesterService.deleteSemester(semesterId);
      if (!success) {
        return sendNotFound(res, 'Semester not found');
      }

      sendSuccess(res, null, 'Semester deleted successfully');
    } catch (error) {
      if (error instanceof Error && error.message.includes('associated')) {
        return sendBadRequest(res, error.message);
      }

      logger.error('Error deleting semester', { semesterId: req.params.semesterId, error });
      sendInternalServerError(res, 'Failed to delete semester');
    }
  });

  /**
   * POST /api/semesters/:semesterId/activate
   * Activate semester
   */
  static activateSemester = asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    try {
      const { semesterId } = req.params;

      const semester = await SemesterService.activateSemester(semesterId);
      if (!semester) {
        return sendNotFound(res, 'Semester not found');
      }

      sendSuccess(res, semester, 'Semester activated successfully');
    } catch (error) {
      logger.error('Error activating semester', { semesterId: req.params.semesterId, error });
      sendInternalServerError(res, 'Failed to activate semester');
    }
  });
}