services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: schedule_assistant_db
    environment:
      POSTGRES_DB: schedule_assistant
      POSTGRES_USER: schedule_user
      POSTGRES_PASSWORD: schedule_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database_schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    networks:
      - schedule_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U schedule_user -d schedule_assistant"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and job queue
  redis:
    image: redis:7-alpine
    container_name: schedule_assistant_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - schedule_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API (Development)
  api:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: schedule_assistant_api
    environment:
      DATABASE_URL: **********************************************************/schedule_assistant?schema=public
      REDIS_URL: redis://redis:6379
      NODE_ENV: development
      PORT: 3001
    ports:
      - "3001:3001"
    volumes:
      - .:/app
      - /app/node_modules
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - schedule_network
    stdin_open: true
    tty: true

  # Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
      target: development
    container_name: schedule_assistant_frontend
    environment:
      VITE_API_URL: http://localhost:3001
      NODE_ENV: development
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - api
    networks:
      - schedule_network
    stdin_open: true
    tty: true

  # Nginx (Production-like setup)
  nginx:
    image: nginx:alpine
    container_name: schedule_assistant_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
      - frontend
    networks:
      - schedule_network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:

networks:
  schedule_network:
    driver: bridge