@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Stop Script
:: This script safely stops all application services

echo ========================================
echo University Schedule Assistant - Stop
echo ========================================
echo.

:: Set color for better visibility
color 0C

echo [INFO] Stopping University Schedule Assistant...
echo.

:: Stop all services
echo [INFO] Stopping all services...
docker-compose down
if %errorLevel% neq 0 (
    echo [ERROR] Failed to stop some services
    echo [INFO] Checking running containers...
    docker ps
) else (
    echo [SUCCESS] All services stopped successfully
)
echo.

:: Show final status
echo [INFO] Final Status:
echo ========================================
docker-compose ps
echo.

:: Cleanup options
echo [INFO] Cleanup Options:
echo ========================================
echo [INFO] Would you like to remove unused Docker resources? (Y/N)
set /p "cleanup="
if /i "!cleanup!"=="Y" (
    echo [INFO] Cleaning up unused Docker resources...
    docker system prune -f
    echo [SUCCESS] Cleanup completed
) else (
    echo [INFO] Skipping cleanup
)

echo.
echo [SUCCESS] Application stopped successfully!
echo [INFO] To start again, run: start-application.bat
pause