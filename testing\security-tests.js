/**
 * Security Testing Suite for University Schedule Assistant
 * Tests for common security vulnerabilities and attack vectors
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
    baseURL: process.env.TEST_BASE_URL || 'http://localhost:3001',
    frontendURL: process.env.TEST_FRONTEND_URL || 'http://localhost:3000',
    testTimeout: 30000
};

// Test results storage
const securityResults = {
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    vulnerabilities: [],
    warnings: [],
    timestamp: new Date().toISOString()
};

// Utility functions
const logTest = (testName, result, details = '') => {
    const status = result ? 'PASS' : 'FAIL';
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${status}] ${testName} ${details}`);

    securityResults.totalTests++;
    if (result) {
        securityResults.passedTests++;
    } else {
        securityResults.failedTests++;
        securityResults.vulnerabilities.push({
            test: testName,
            details: details,
            timestamp: timestamp
        });
    }
};

const logWarning = (testName, message) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [WARN] ${testName}: ${message}`);
    securityResults.warnings.push({
        test: testName,
        message: message,
        timestamp: timestamp
    });
};

const makeSecureRequest = async (method, endpoint, data = null, headers = {}) => {
    try {
        const config = {
            method,
            url: `${CONFIG.baseURL}${endpoint}`,
            timeout: CONFIG.testTimeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Security-Test-Suite',
                ...headers
            },
            validateStatus: () => true // Don't throw on any status code
        };

        if (data) {
            config.data = data;
        }

        const response = await axios(config);
        return {
            status: response.status,
            headers: response.headers,
            data: response.data
        };
    } catch (error) {
        return {
            status: 0,
            error: error.message,
            headers: {},
            data: null
        };
    }
};

// Security test suites
const testSQLInjection = async () => {
    console.log('\n=== SQL Injection Tests ===');

    const sqlPayloads = [
        "'; DROP TABLE departments; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
        "'; INSERT INTO departments (name) VALUES ('hacked'); --",
        "' OR 1=1 --",
        "admin'--",
        "admin'/*",
        "' OR 'x'='x",
        "'; EXEC xp_cmdshell('dir'); --"
    ];

    // Test SQL injection in department creation
    for (const payload of sqlPayloads) {
        const response = await makeSecureRequest('POST', '/api/departments', {
            departmentName: payload,
            departmentCode: 'TEST'
        });

        // Should not return 200 with malicious payload
        const isSecure = response.status !== 200 ||
                        (response.data && !response.data.success);

        logTest(
            'SQL Injection - Department Name',
            isSecure,
            `Payload: ${payload.substring(0, 20)}... Status: ${response.status}`
        );
    }

    // Test SQL injection in search/filter endpoints
    for (const payload of sqlPayloads.slice(0, 3)) {
        const response = await makeSecureRequest('GET', `/api/departments?search=${encodeURIComponent(payload)}`);

        const isSecure = response.status !== 500 &&
                        (!response.data || !JSON.stringify(response.data).includes('error'));

        logTest(
            'SQL Injection - Search Parameter',
            isSecure,
            `Payload: ${payload.substring(0, 20)}... Status: ${response.status}`
        );
    }
};

const testXSS = async () => {
    console.log('\n=== Cross-Site Scripting (XSS) Tests ===');

    const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("XSS")',
        '<svg onload="alert(1)">',
        '"><script>alert("XSS")</script>',
        '<iframe src="javascript:alert(1)"></iframe>',
        '<body onload="alert(1)">',
        '<input type="text" value="" onfocus="alert(1)" autofocus>'
    ];

    // Test XSS in department creation
    for (const payload of xssPayloads) {
        const response = await makeSecureRequest('POST', '/api/departments', {
            departmentName: payload,
            departmentCode: 'XSS'
        });

        // Check if payload is properly escaped in response
        const isSecure = !response.data ||
                        !JSON.stringify(response.data).includes(payload) ||
                        JSON.stringify(response.data).includes('&lt;') ||
                        JSON.stringify(response.data).includes('&gt;');

        logTest(
            'XSS - Department Name',
            isSecure,
            `Payload: ${payload.substring(0, 30)}... Status: ${response.status}`
        );
    }

    // Test XSS in room creation
    for (const payload of xssPayloads.slice(0, 3)) {
        const response = await makeSecureRequest('POST', '/api/rooms', {
            roomName: payload,
            capacity: 30,
            roomType: 'Classroom'
        });

        const isSecure = !response.data ||
                        !JSON.stringify(response.data).includes(payload) ||
                        JSON.stringify(response.data).includes('&lt;');

        logTest(
            'XSS - Room Name',
            isSecure,
            `Payload: ${payload.substring(0, 30)}... Status: ${response.status}`
        );
    }
};