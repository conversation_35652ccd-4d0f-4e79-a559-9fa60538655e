@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Backup Script
:: This script creates backups of database and application data

echo ========================================
echo University Schedule Assistant - Backup
echo ========================================
echo.

:: Set color for better visibility
color 0E

:: Configuration
set "BACKUP_DIR=backups"
set "TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"
set "BACKUP_PATH=%BACKUP_DIR%\backup_%TIMESTAMP%"

echo [INFO] Creating backup of University Schedule Assistant...
echo [INFO] Backup location: %BACKUP_PATH%
echo.

:: Create backup directory
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
if not exist "%BACKUP_PATH%" mkdir "%BACKUP_PATH%"

:: Check if application is running
docker-compose ps | findstr "Up" >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Application does not appear to be running
    echo [INFO] Some backup operations may fail
    echo.
)

:: Backup database
echo [INFO] Backing up database...
docker-compose exec -T postgres pg_dump -U postgres schedule_assistant > "%BACKUP_PATH%\database_backup.sql" 2>nul
if %errorLevel% equ 0 (
    echo [SUCCESS] Database backup completed
) else (
    echo [ERROR] Database backup failed
    echo [INFO] Make sure the application is running and database is accessible
)

:: Backup uploads directory
echo [INFO] Backing up uploads directory...
if exist "uploads" (
    xcopy "uploads" "%BACKUP_PATH%\uploads" /E /I /Q >nul 2>&1
    if %errorLevel% equ 0 (
        echo [SUCCESS] Uploads backup completed
    ) else (
        echo [WARNING] Uploads backup failed or directory is empty
    )
) else (
    echo [WARNING] Uploads directory not found
)

:: Backup logs
echo [INFO] Backing up logs...
if exist "logs" (
    xcopy "logs" "%BACKUP_PATH%\logs" /E /I /Q >nul 2>&1
    if %errorLevel% equ 0 (
        echo [SUCCESS] Logs backup completed
    ) else (
        echo [WARNING] Logs backup failed or directory is empty
    )
) else (
    echo [WARNING] Logs directory not found
)

:: Backup configuration files
echo [INFO] Backing up configuration files...
if exist ".env" copy ".env" "%BACKUP_PATH%\.env" >nul 2>&1
if exist "docker-compose.yml" copy "docker-compose.yml" "%BACKUP_PATH%\docker-compose.yml" >nul 2>&1
if exist "docker-compose.prod.yml" copy "docker-compose.prod.yml" "%BACKUP_PATH%\docker-compose.prod.yml" >nul 2>&1
echo [SUCCESS] Configuration files backup completed

:: Create backup info file
echo [INFO] Creating backup information file...
(
    echo Backup Information
    echo ==================
    echo Backup Date: %date% %time%
    echo Backup Location: %BACKUP_PATH%
    echo.
    echo Contents:
    echo - Database dump: database_backup.sql
    echo - Uploads directory: uploads/
    echo - Logs directory: logs/
    echo - Configuration files: .env, docker-compose files
    echo.
    echo Application Status at Backup Time:
) > "%BACKUP_PATH%\backup_info.txt"

:: Add container status to backup info
docker-compose ps >> "%BACKUP_PATH%\backup_info.txt" 2>&1

echo [SUCCESS] Backup information file created
echo.

:: Show backup summary
echo [INFO] Backup Summary:
echo ========================================
echo Backup Location: %BACKUP_PATH%
echo.
dir "%BACKUP_PATH%" /B
echo.

:: Calculate backup size
for /f "tokens=3" %%a in ('dir "%BACKUP_PATH%" /-c ^| findstr /C:" bytes"') do set "backup_size=%%a"
echo Backup Size: %backup_size% bytes
echo.

:: Cleanup old backups (keep last 10)
echo [INFO] Cleaning up old backups (keeping last 10)...
set /a "count=0"
for /f "skip=10 delims=" %%i in ('dir "%BACKUP_DIR%\backup_*" /b /o-d 2^>nul') do (
    set /a "count+=1"
    rmdir /s /q "%BACKUP_DIR%\%%i" 2>nul
)
if %count% gtr 0 (
    echo [SUCCESS] Removed %count% old backup(s)
) else (
    echo [INFO] No old backups to remove
)
echo.

:: Offer to compress backup
echo [INFO] Would you like to compress the backup? (Y/N)
set /p "compress="
if /i "!compress!"=="Y" (
    echo [INFO] Compressing backup...
    powershell -command "Compress-Archive -Path '%BACKUP_PATH%' -DestinationPath '%BACKUP_PATH%.zip' -Force"
    if %errorLevel% equ 0 (
        echo [SUCCESS] Backup compressed to %BACKUP_PATH%.zip
        echo [INFO] Would you like to remove the uncompressed backup? (Y/N)
        set /p "remove_uncompressed="
        if /i "!remove_uncompressed!"=="Y" (
            rmdir /s /q "%BACKUP_PATH%"
            echo [SUCCESS] Uncompressed backup removed
        )
    ) else (
        echo [ERROR] Backup compression failed
    )
)

echo.
echo [SUCCESS] Backup completed successfully!
echo [INFO] Backup location: %BACKUP_PATH%
echo.
echo [INFO] To restore from this backup:
echo 1. Stop the application: stop-application.bat
echo 2. Restore database: docker-compose exec -T postgres psql -U postgres -d schedule_assistant ^< %BACKUP_PATH%\database_backup.sql
echo 3. Restore files: copy files from %BACKUP_PATH% to application directories
echo 4. Start application: start-application.bat
pause