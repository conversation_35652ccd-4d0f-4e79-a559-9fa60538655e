/**
 * Test Data Generation Script for University Schedule Assistant
 * Generates comprehensive test data for user testing scenarios
 */

const XLSX = require('xlsx');
const fs = require('fs');
const path = require('path');

// Ensure test-data directory exists
const testDataDir = path.join(__dirname, 'test-data');
if (!fs.existsSync(testDataDir)) {
    fs.mkdirSync(testDataDir, { recursive: true });
}

// Sample data generators
const generateDepartments = () => {
    return [
        { 'Department Name': 'Computer Science', 'Department Code': 'CS' },
        { 'Department Name': 'Mathematics', 'Department Code': 'MATH' },
        { 'Department Name': 'Physics', 'Department Code': 'PHYS' },
        { 'Department Name': 'Chemistry', 'Department Code': 'CHEM' },
        { 'Department Name': 'Biology', 'Department Code': 'BIO' },
        { 'Department Name': 'Engineering', 'Department Code': 'ENG' },
        { 'Department Name': 'Business Administration', 'Department Code': 'BA' },
        { 'Department Name': 'Psychology', 'Department Code': 'PSY' },
        { 'Department Name': 'English Literature', 'Department Code': 'ENG-LIT' },
        { 'Department Name': 'History', 'Department Code': 'HIST' }
    ];
};

const generateRooms = () => {
    return [
        {
            'Room Name': 'Room 101',
            'Capacity': 30,
            'Room Type': 'Lecture Hall',
            'Building': 'Main Building',
            'Floor': '1st Floor',
            'Equipment': 'Projector, Whiteboard, Audio System'
        },
        {
            'Room Name': 'Room 102',
            'Capacity': 25,
            'Room Type': 'Classroom',
            'Building': 'Main Building',
            'Floor': '1st Floor',
            'Equipment': 'Whiteboard, Computer'
        },
        {
            'Room Name': 'Lab A',
            'Capacity': 20,
            'Room Type': 'Laboratory',
            'Building': 'Science Building',
            'Floor': '2nd Floor',
            'Equipment': 'Computers, Projector, Lab Equipment'
        },
        {
            'Room Name': 'Lab B',
            'Capacity': 15,
            'Room Type': 'Laboratory',
            'Building': 'Science Building',
            'Floor': '2nd Floor',
            'Equipment': 'Lab Equipment, Fume Hood, Safety Equipment'
        },
        {
            'Room Name': 'Auditorium',
            'Capacity': 200,
            'Room Type': 'Auditorium',
            'Building': 'Main Building',
            'Floor': 'Ground Floor',
            'Equipment': 'Audio System, Projector, Stage Lighting'
        },
        {
            'Room Name': 'Room 201',
            'Capacity': 35,
            'Room Type': 'Lecture Hall',
            'Building': 'Academic Building',
            'Floor': '2nd Floor',
            'Equipment': 'Smart Board, Projector, Audio System'
        },
        {
            'Room Name': 'Room 202',
            'Capacity': 28,
            'Room Type': 'Classroom',
            'Building': 'Academic Building',
            'Floor': '2nd Floor',
            'Equipment': 'Whiteboard, Computer, Projector'
        },
        {
            'Room Name': 'Computer Lab 1',
            'Capacity': 25,
            'Room Type': 'Computer Lab',
            'Building': 'Technology Building',
            'Floor': '1st Floor',
            'Equipment': 'Computers, Projector, Network Access'
        },
        {
            'Room Name': 'Computer Lab 2',
            'Capacity': 30,
            'Room Type': 'Computer Lab',
            'Building': 'Technology Building',
            'Floor': '1st Floor',
            'Equipment': 'Computers, Smart Board, Network Access'
        },
        {
            'Room Name': 'Seminar Room',
            'Capacity': 15,
            'Room Type': 'Seminar Room',
            'Building': 'Academic Building',
            'Floor': '3rd Floor',
            'Equipment': 'Conference Table, Projector, Video Conferencing'
        }
    ];
};

const generateInstructors = () => {
    return [
        {
            'First Name': 'John',
            'Last Name': 'Smith',
            'Email': '<EMAIL>',
            'Phone': '******-0101',
            'Office': 'CS-201',
            'Department Code': 'CS',
            'Max Hours Per Day': 8,
            'Max Hours Per Week': 40
        },
        {
            'First Name': 'Sarah',
            'Last Name': 'Johnson',
            'Email': '<EMAIL>',
            'Phone': '******-0102',
            'Office': 'MATH-101',
            'Department Code': 'MATH',
            'Max Hours Per Day': 6,
            'Max Hours Per Week': 30
        },
        {
            'First Name': 'Michael',
            'Last Name': 'Brown',
            'Email': '<EMAIL>',
            'Phone': '******-0103',
            'Office': 'PHYS-301',
            'Department Code': 'PHYS',
            'Max Hours Per Day': 7,
            'Max Hours Per Week': 35
        },
        {
            'First Name': 'Emily',
            'Last Name': 'Davis',
            'Email': '<EMAIL>',
            'Phone': '******-0104',
            'Office': 'CHEM-201',
            'Department Code': 'CHEM',
            'Max Hours Per Day': 8,
            'Max Hours Per Week': 40
        },
        {
            'First Name': 'Robert',
            'Last Name': 'Wilson',
            'Email': '<EMAIL>',
            'Phone': '******-0105',
            'Office': 'BIO-101',
            'Department Code': 'BIO',
            'Max Hours Per Day': 6,
            'Max Hours Per Week': 32
        },
        {
            'First Name': 'Lisa',
            'Last Name': 'Anderson',
            'Email': '<EMAIL>',
            'Phone': '******-0106',
            'Office': 'ENG-401',
            'Department Code': 'ENG',
            'Max Hours Per Day': 8,
            'Max Hours Per Week': 40
        },
        {
            'First Name': 'David',
            'Last Name': 'Taylor',
            'Email': '<EMAIL>',
            'Phone': '******-0107',
            'Office': 'BA-301',
            'Department Code': 'BA',
            'Max Hours Per Day': 7,
            'Max Hours Per Week': 35
        },
        {
            'First Name': 'Jennifer',
            'Last Name': 'Martinez',
            'Email': '<EMAIL>',
            'Phone': '******-0108',
            'Office': 'PSY-201',
            'Department Code': 'PSY',
            'Max Hours Per Day': 6,
            'Max Hours Per Week': 30
        },
        {
            'First Name': 'Christopher',
            'Last Name': 'Garcia',
            'Email': '<EMAIL>',
            'Phone': '******-0109',
            'Office': 'ENG-LIT-101',
            'Department Code': 'ENG-LIT',
            'Max Hours Per Day': 8,
            'Max Hours Per Week': 38
        },
        {
            'First Name': 'Amanda',
            'Last Name': 'Rodriguez',
            'Email': '<EMAIL>',
            'Phone': '******-0110',
            'Office': 'HIST-201',
            'Department Code': 'HIST',
            'Max Hours Per Day': 7,
            'Max Hours Per Week': 35
        }
    ];
};

const generateCourses = () => {
    return [
        {
            'Course Code': 'CS101',
            'Course Name': 'Introduction to Programming',
            'Department Code': 'CS',
            'Instructor Email': '<EMAIL>',
            'Credits': 3,
            'Sessions Per Week': 2,
            'Session Duration': 90,
            'Min Room Capacity': 25,
            'Required Room Type': 'Computer Lab',
            'Priority': 'HIGH'
        },
        {
            'Course Code': 'CS201',
            'Course Name': 'Data Structures',
            'Department Code': 'CS',
            'Instructor Email': '<EMAIL>',
            'Credits': 3,
            'Sessions Per Week': 2,
            'Session Duration': 90,
            'Min Room Capacity': 25,
            'Required Room Type': 'Computer Lab',
            'Priority': 'HIGH'
        },
        {
            'Course Code': 'MATH101',
            'Course Name': 'Calculus I',
            'Department Code': 'MATH',
            'Instructor Email': '<EMAIL>',
            'Credits': 4,
            'Sessions Per Week': 3,
            'Session Duration': 60,
            'Min Room Capacity': 30,
            'Required Room Type': 'Lecture Hall',
            'Priority': 'HIGH'
        },
        {
            'Course Code': 'MATH201',
            'Course Name': 'Calculus II',
            'Department Code': 'MATH',
            'Instructor Email': '<EMAIL>',
            'Credits': 4,
            'Sessions Per Week': 3,
            'Session Duration': 60,
            'Min Room Capacity': 25,
            'Required Room Type': 'Lecture Hall',
            'Priority': 'MEDIUM'
        },
        {
            'Course Code': 'PHYS101',
            'Course Name': 'General Physics I',
            'Department Code': 'PHYS',
            'Instructor Email': '<EMAIL>',
            'Credits': 4,
            'Sessions Per Week': 2,
            'Session Duration': 120,
            'Min Room Capacity': 20,
            'Required Room Type': 'Laboratory',
            'Priority': 'HIGH'
        },
        {
            'Course Code': 'CHEM101',
            'Course Name': 'General Chemistry',
            'Department Code': 'CHEM',
            'Instructor Email': '<EMAIL>',
            'Credits': 4,
            'Sessions Per Week': 2,
            'Session Duration': 90,
            'Min Room Capacity': 15,
            'Required Room Type': 'Laboratory',
            'Priority': 'HIGH'
        },
        {
            'Course Code': 'BIO101',
            'Course Name': 'Introduction to Biology',
            'Department Code': 'BIO',
            'Instructor Email': '<EMAIL>',
            'Credits': 3,
            'Sessions Per Week': 2,
            'Session Duration': 90,
            'Min Room Capacity': 20,
            'Required Room Type': 'Laboratory',
            'Priority': 'MEDIUM'
        },
        {
            'Course Code': 'ENG101',
            'Course Name': 'Engineering Fundamentals',
            'Department Code': 'ENG',
            'Instructor Email': '<EMAIL>',
            'Credits': 3,
            'Sessions Per Week': 2,
            'Session Duration': 90,
            'Min Room Capacity': 30,
            'Required Room Type': 'Lecture Hall',
            'Priority': 'HIGH'
        },
        {
            'Course Code': 'BA101',
            'Course Name': 'Introduction to Business',
            'Department Code': 'BA',
            'Instructor Email': '<EMAIL>',
            'Credits': 3,
            'Sessions Per Week': 2,
            'Session Duration': 75,
            'Min Room Capacity': 35,
            'Required Room Type': 'Lecture Hall',
            'Priority': 'MEDIUM'
        },
        {
            'Course Code': 'PSY101',
            'Course Name': 'Introduction to Psychology',
            'Department Code': 'PSY',
            'Instructor Email': '<EMAIL>',
            'Credits': 3,
            'Sessions Per Week': 2,
            'Session Duration': 75,
            'Min Room Capacity': 30,
            'Required Room Type': 'Classroom',
            'Priority': 'MEDIUM'
        }
    ];
};

// Generate invalid test data for error testing
const generateInvalidDepartments = () => {
    return [
        { 'Department Name': '', 'Department Code': 'INVALID1' }, // Empty name
        { 'Department Name': 'Valid Department', 'Department Code': '' }, // Empty code
        { 'Department Name': 'Computer Science', 'Department Code': 'CS' }, // Duplicate
        { 'Department Name': 'Test Department', 'Department Code': 'CS' }, // Duplicate code
    ];
};

const generateInvalidRooms = () => {
    return [
        { 'Room Name': '', 'Capacity': 30 }, // Empty name
        { 'Room Name': 'Invalid Room', 'Capacity': '' }, // Empty capacity
        { 'Room Name': 'Invalid Room 2', 'Capacity': -5 }, // Negative capacity
        { 'Room Name': 'Invalid Room 3', 'Capacity': 'not a number' }, // Invalid capacity
    ];
};

// Excel file generation functions
const createExcelFile = (data, filename) => {
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(data);
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    const filePath = path.join(testDataDir, filename);
    XLSX.writeFile(workbook, filePath);
    console.log(`Generated: ${filePath}`);
};

// Main generation function
const generateAllTestData = () => {
    console.log('Generating test data for University Schedule Assistant...');

    // Valid test data
    createExcelFile(generateDepartments(), 'departments_valid.xlsx');
    createExcelFile(generateRooms(), 'rooms_valid.xlsx');
    createExcelFile(generateInstructors(), 'instructors_valid.xlsx');
    createExcelFile(generateCourses(), 'courses_valid.xlsx');

    // Invalid test data for error testing
    createExcelFile(generateInvalidDepartments(), 'departments_invalid.xlsx');
    createExcelFile(generateInvalidRooms(), 'rooms_invalid.xlsx');

    // Large dataset for performance testing
    const largeDepartments = [];
    const largeRooms = [];
    const largeInstructors = [];
    const largeCourses = [];

    // Generate large datasets
    for (let i = 1; i <= 100; i++) {
        largeDepartments.push({
            'Department Name': `Department ${i}`,
            'Department Code': `DEPT${i.toString().padStart(3, '0')}`
        });
    }

    for (let i = 1; i <= 500; i++) {
        largeRooms.push({
            'Room Name': `Room ${i}`,
            'Capacity': Math.floor(Math.random() * 100) + 10,
            'Room Type': ['Classroom', 'Laboratory', 'Lecture Hall', 'Auditorium'][Math.floor(Math.random() * 4)],
            'Building': `Building ${Math.ceil(i / 50)}`,
            'Floor': `${Math.floor(Math.random() * 5) + 1}${['st', 'nd', 'rd', 'th'][Math.min(Math.floor(Math.random() * 5), 3)]} Floor`,
            'Equipment': 'Projector, Whiteboard'
        });
    }

    createExcelFile(largeDepartments, 'departments_large.xlsx');
    createExcelFile(largeRooms, 'rooms_large.xlsx');

    console.log('Test data generation completed!');
    console.log(`Files generated in: ${testDataDir}`);
};

// Run the generation
if (require.main === module) {
    generateAllTestData();
}

module.exports = {
    generateDepartments,
    generateRooms,
    generateInstructors,
    generateCourses,
    generateInvalidDepartments,
    generateInvalidRooms,
    generateAllTestData
};