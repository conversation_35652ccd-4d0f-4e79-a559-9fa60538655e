# University Schedule Assistant - User Testing Guide

## Overview
This comprehensive testing guide provides structured scenarios for testing the University Schedule Assistant system. It covers all major user workflows, edge cases, and system functionality.

## Testing Environment Setup

### Prerequisites
- Access to the deployed application
- Test data sets (provided in `test-data/` directory)
- Multiple user accounts with different roles
- Excel files for import/export testing

### Test User Accounts
```
Administrator: <EMAIL> / admin123
Department Head: <EMAIL> / dept123
Scheduler: <EMAIL> / schedule123
Instructor: <EMAIL> / instructor123
```

## Test Scenarios

### 1. System Setup and Initial Configuration

#### 1.1 First-Time Setup
**Objective**: Verify system initialization and basic configuration

**Steps**:
1. Access the application at the deployed URL
2. Verify the welcome screen displays correctly
3. Navigate through the main sections (Dashboard, Departments, Rooms, etc.)
4. Check that all navigation links work properly
5. Verify responsive design on different screen sizes

**Expected Results**:
- Application loads without errors
- All navigation elements are functional
- UI is responsive and user-friendly
- No broken links or missing resources

#### 1.2 Database Connection Test
**Objective**: Ensure database connectivity and basic operations

**Steps**:
1. Navigate to any data management section
2. Attempt to view existing data
3. Try creating a new record
4. Verify data persistence after page refresh

**Expected Results**:
- Data loads correctly from database
- New records can be created and saved
- Data persists across sessions

### 2. Department Management Testing

#### 2.1 Department CRUD Operations
**Objective**: Test complete department lifecycle management

**Steps**:
1. Navigate to Departments section
2. **Create**: Add new department "Computer Science" with code "CS"
3. **Read**: Verify department appears in the list
4. **Update**: Edit department name to "Computer Science & Engineering"
5. **Delete**: Remove a test department
6. Test validation by trying to create duplicate department codes

**Expected Results**:
- All CRUD operations work correctly
- Validation prevents duplicate codes
- Changes are reflected immediately in the UI
- Confirmation dialogs appear for destructive actions

#### 2.2 Department Data Validation
**Objective**: Test input validation and error handling

**Test Cases**:
- Empty department name
- Empty department code
- Duplicate department codes
- Special characters in codes
- Very long department names

**Expected Results**:
- Appropriate error messages for invalid inputs
- Form prevents submission with invalid data
- User-friendly error messaging

### 3. Room Management Testing

#### 3.1 Room Creation and Management
**Objective**: Test room management functionality

**Steps**:
1. Navigate to Rooms section
2. Create room "Room 101" with capacity 30
3. Add equipment: "Projector", "Whiteboard", "Audio System"
4. Set room type as "Lecture Hall"
5. Add building and floor information
6. Test room editing and deletion

**Expected Results**:
- Rooms can be created with all attributes
- Equipment can be added/removed dynamically
- Room information displays correctly
- Capacity validation works (positive numbers only)

#### 3.2 Room Search and Filtering
**Objective**: Test room discovery functionality

**Steps**:
1. Create multiple rooms with different attributes
2. Test search by room name
3. Filter by capacity range
4. Filter by room type
5. Filter by available equipment

**Expected Results**:
- Search returns accurate results
- Filters work independently and in combination
- Results update in real-time
- Clear filter option works correctly

### 4. Instructor Management Testing

#### 4.1 Instructor Profile Management
**Objective**: Test instructor data management

**Steps**:
1. Navigate to Instructors section
2. Add new instructor with complete profile
3. Assign to department
4. Set availability constraints (max hours per day/week)
5. Add contact information
6. Test profile editing and updates

**Expected Results**:
- Complete instructor profiles can be created
- Department assignment works correctly
- Availability constraints are saved
- Profile information displays properly

#### 4.2 Instructor Availability Testing
**Objective**: Test availability constraint functionality

**Steps**:
1. Set instructor max hours per day: 6
2. Set instructor max hours per week: 30
3. Create courses that would exceed these limits
4. Attempt to generate schedule
5. Verify constraint violations are detected

**Expected Results**:
- Availability constraints are enforced
- Schedule generation respects limits
- Clear error messages for constraint violations

### 5. Course Management Testing

#### 5.1 Course Creation and Configuration
**Objective**: Test course setup and configuration

**Steps**:
1. Navigate to Courses section
2. Create course "CS101 - Introduction to Programming"
3. Assign instructor and department
4. Set sessions per week: 2
5. Set session duration: 90 minutes
6. Set minimum room capacity: 25
7. Specify required room type and equipment

**Expected Results**:
- Courses can be created with all attributes
- Instructor and department assignments work
- Scheduling preferences are saved correctly
- Room requirements are properly configured

#### 5.2 Course Scheduling Requirements
**Objective**: Test course scheduling constraint setup

**Steps**:
1. Create course with specific room requirements
2. Set priority level (HIGH, MEDIUM, LOW)
3. Add time preferences if available
4. Test course editing and updates
5. Verify constraint validation

**Expected Results**:
- Room requirements are properly set
- Priority levels affect scheduling
- Time preferences are saved
- Validation prevents invalid configurations

### 6. Schedule Generation Testing

#### 6.1 Basic Schedule Generation
**Objective**: Test core scheduling functionality

**Prerequisites**:
- At least 3 departments created
- 5+ rooms with different capacities
- 10+ instructors assigned to departments
- 15+ courses with various requirements

**Steps**:
1. Navigate to Scheduling section
2. Select semester for scheduling
3. Verify all required data is present
4. Initiate schedule generation
5. Monitor generation progress
6. Review generated schedule

**Expected Results**:
- Schedule generation completes successfully
- All courses are assigned time slots
- No scheduling conflicts exist
- Room capacities are respected
- Instructor availability is honored