# University Schedule Assistant - Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the University Schedule Assistant to production environments.

## Prerequisites

### System Requirements
- **Operating System**: Linux (Ubuntu 20.04+ recommended) or Windows Server
- **Memory**: Minimum 4GB RAM (8GB+ recommended for production)
- **Storage**: Minimum 20GB free space
- **Network**: Stable internet connection for initial setup

### Required Software
- **Docker**: Version 20.10 or higher
- **Docker Compose**: Version 2.0 or higher
- **Git**: For cloning the repository
- **SSL Certificate**: For HTTPS (recommended for production)

## Pre-Deployment Setup

### 1. Server Preparation
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Add user to docker group
sudo usermod -aG docker $USER
newgrp docker
```

### 2. Clone Repository
```bash
git clone https://github.com/your-organization/schedule-assistant.git
cd schedule-assistant
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.production .env

# Edit environment variables
nano .env
```

#### Required Environment Variables
```env
# Database Configuration
POSTGRES_DB=schedule_assistant_prod
POSTGRES_USER=schedule_user
POSTGRES_PASSWORD=your_secure_database_password

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password

# Application Security
JWT_SECRET=your_very_secure_jwt_secret_key_minimum_32_characters
SESSION_SECRET=your_secure_session_secret

# Application URLs
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://api.your-domain.com

# Email Configuration (optional)
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_FROM=<EMAIL>
```

### 4. SSL Certificate Setup (Production)
```bash
# Create SSL directory
mkdir -p nginx/ssl

# Copy your SSL certificates
cp /path/to/your/certificate.crt nginx/ssl/cert.pem
cp /path/to/your/private.key nginx/ssl/key.pem

# Set proper permissions
chmod 600 nginx/ssl/key.pem
chmod 644 nginx/ssl/cert.pem
```

## Deployment Methods

### Method 1: Automated Deployment (Recommended)
```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Run deployment
./scripts/deploy.sh production
```

### Method 2: Manual Deployment
```bash
# Create necessary directories
mkdir -p backups logs uploads

# Build and start services
docker-compose -f docker-compose.prod.yml up -d --build

# Wait for services to be ready
sleep 30

# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend npx prisma migrate deploy

# Generate Prisma client
docker-compose -f docker-compose.prod.yml exec backend npx prisma generate
```

## Post-Deployment Configuration

### 1. Verify Deployment
```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Check logs
docker-compose -f docker-compose.prod.yml logs

# Test health endpoints
curl http://localhost:3001/health
curl http://localhost:3000
```

### 2. Initial Data Setup
```bash
# Generate test data (optional)
cd testing
node generate-test-data.js

# Access the application
# Navigate to http://your-domain.com
# Use the Excel import feature to load initial data
```

### 3. Create Admin User
```bash
# Connect to the database
docker-compose -f docker-compose.prod.yml exec postgres psql -U postgres -d schedule_assistant_prod

# Create admin user (example)
INSERT INTO users (email, password_hash, role, created_at, updated_at)
VALUES ('<EMAIL>', '$2b$10$hashed_password', 'ADMIN', NOW(), NOW());
```

## Domain and SSL Configuration

### 1. DNS Configuration
Point your domain to the server IP address:
```
A Record: your-domain.com → YOUR_SERVER_IP
A Record: api.your-domain.com → YOUR_SERVER_IP
```

### 2. Nginx SSL Configuration
Edit `nginx/nginx.conf` to enable HTTPS:
```nginx
# Uncomment and configure the HTTPS server block
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    # ... rest of configuration
}
```

### 3. Restart Nginx
```bash
docker-compose -f docker-compose.prod.yml restart nginx
```

## Monitoring and Maintenance

### 1. Log Management
```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend
docker-compose -f docker-compose.prod.yml logs -f nginx

# Log rotation (add to crontab)
0 2 * * * docker-compose -f /path/to/schedule-assistant/docker-compose.prod.yml exec postgres pg_dump -U postgres schedule_assistant_prod > /path/to/backups/backup_$(date +\%Y\%m\%d).sql
```

### 2. Database Backups
```bash
# Manual backup
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U postgres schedule_assistant_prod > backups/backup_$(date +%Y%m%d_%H%M%S).sql

# Automated backup (add to crontab)
0 2 * * * /path/to/schedule-assistant/scripts/backup.sh
```

### 3. System Updates
```bash
# Update application
git pull origin main
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml up -d --build

# Update system packages
sudo apt update && sudo apt upgrade -y
```