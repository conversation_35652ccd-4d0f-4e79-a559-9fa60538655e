import { useState } from "react";
import { FileSpreadsheet, Upload, Download, Tabs } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { ExcelImport } from "~/components/excel/ExcelImport";
import { ExcelExport } from "~/components/excel/ExcelExport";

export default function ExcelPage() {
  const [activeTab, setActiveTab] = useState<'import' | 'export'>('import');
  const [selectedSemester, setSelectedSemester] = useState<string>('');

  // Mock semester data - in real app, this would come from API
  const semesters = [
    { id: '1', name: 'Fall 2024', isActive: true },
    { id: '2', name: 'Spring 2024', isActive: false },
    { id: '3', name: 'Summer 2024', isActive: false },
  ];

  const handleImportComplete = (result: any) => {
    console.log('Import completed:', result);
    // Refresh data or show success message
  };

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Excel Integration</h2>
          <p className="text-muted-foreground">
            Import and export data using Excel files
          </p>
        </div>
        <FileSpreadsheet className="h-8 w-8 text-primary" />
      </div>

      {/* Semester Selection */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
        <h3 className="text-lg font-semibold mb-4">Semester Selection</h3>
        <div className="flex items-center space-x-4">
          <label className="text-sm font-medium">Select Semester:</label>
          <select
            value={selectedSemester}
            onChange={(e) => setSelectedSemester(e.target.value)}
            className="px-3 py-2 border border-input rounded-md bg-background"
          >
            <option value="">Select a semester...</option>
            {semesters.map((semester) => (
              <option key={semester.id} value={semester.id}>
                {semester.name} {semester.isActive && '(Active)'}
              </option>
            ))}
          </select>
        </div>
        <p className="text-sm text-muted-foreground mt-2">
          Some operations require a semester to be selected
        </p>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 rounded-lg bg-muted p-1">
        <button
          onClick={() => setActiveTab('import')}
          className={`flex-1 flex items-center justify-center space-x-2 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
            activeTab === 'import'
              ? 'bg-background text-foreground shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          <Upload className="h-4 w-4" />
          <span>Import Data</span>
        </button>
        <button
          onClick={() => setActiveTab('export')}
          className={`flex-1 flex items-center justify-center space-x-2 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
            activeTab === 'export'
              ? 'bg-background text-foreground shadow-sm'
              : 'text-muted-foreground hover:text-foreground'
          }`}
        >
          <Download className="h-4 w-4" />
          <span>Export Data</span>
        </button>
      </div>

      {/* Tab Content */}
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        {activeTab === 'import' && (
          <div className="p-6">
            <div className="space-y-8">
              {/* Import Sections */}
              <div className="grid gap-8 lg:grid-cols-2">
                <div className="space-y-6">
                  <ExcelImport
                    type="departments"
                    onImportComplete={handleImportComplete}
                  />
                </div>
                <div className="space-y-6">
                  <ExcelImport
                    type="rooms"
                    onImportComplete={handleImportComplete}
                  />
                </div>
              </div>

              <div className="grid gap-8 lg:grid-cols-2">
                <div className="space-y-6">
                  <ExcelImport
                    type="instructors"
                    onImportComplete={handleImportComplete}
                  />
                </div>
                <div className="space-y-6">
                  <ExcelImport
                    type="courses"
                    semesterID={selectedSemester}
                    onImportComplete={handleImportComplete}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'export' && (
          <div className="p-6">
            <ExcelExport
              semesterID={selectedSemester}
              semesterName={semesters.find(s => s.id === selectedSemester)?.name}
            />
          </div>
        )}
      </div>

      {/* Help Section */}
      <div className="rounded-lg border border-blue-200 bg-blue-50 p-6">
        <h3 className="text-lg font-semibold text-blue-800 mb-4">Excel Integration Help</h3>
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Import Process</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Download the appropriate template</li>
              <li>• Fill in your data following the format</li>
              <li>• Upload the completed file</li>
              <li>• Review import results and fix any errors</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">Export Features</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Export complete schedules with statistics</li>
              <li>• Download current data for backup</li>
              <li>• Share data with external systems</li>
              <li>• Generate reports for administration</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}