#!/bin/bash

# Production Deployment Script for University Schedule Assistant
# Usage: ./scripts/deploy.sh [environment]

set -e

# Configuration
ENVIRONMENT=${1:-production}
PROJECT_NAME="schedule-assistant"
BACKUP_DIR="./backups"
LOG_FILE="./logs/deployment.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Create necessary directories
create_directories() {
    log "Creating necessary directories..."
    mkdir -p "$BACKUP_DIR"
    mkdir -p "./logs"
    mkdir -p "./uploads"
    mkdir -p "./nginx/ssl"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."

    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi

    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi

    # Check if .env file exists
    if [ ! -f ".env" ]; then
        warning ".env file not found. Copying from .env.production template..."
        cp .env.production .env
        warning "Please edit .env file with your actual configuration values before proceeding."
        read -p "Press Enter to continue after editing .env file..."
    fi

    success "Prerequisites check completed"
}

# Backup existing data
backup_data() {
    log "Creating backup of existing data..."

    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    BACKUP_PATH="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"

    mkdir -p "$BACKUP_PATH"

    # Backup database if container exists
    if docker ps -a | grep -q "${PROJECT_NAME}_postgres"; then
        log "Backing up database..."
        docker exec "${PROJECT_NAME}_postgres" pg_dumpall -U postgres > "$BACKUP_PATH/database_backup.sql" || warning "Database backup failed"
    fi

    # Backup uploads directory
    if [ -d "./uploads" ]; then
        log "Backing up uploads..."
        cp -r ./uploads "$BACKUP_PATH/" || warning "Uploads backup failed"
    fi

    # Backup logs
    if [ -d "./logs" ]; then
        log "Backing up logs..."
        cp -r ./logs "$BACKUP_PATH/" || warning "Logs backup failed"
    fi

    success "Backup created at $BACKUP_PATH"
}

# Build and deploy
deploy() {
    log "Starting deployment for environment: $ENVIRONMENT"

    # Pull latest images
    log "Pulling latest base images..."
    docker-compose -f docker-compose.prod.yml pull

    # Build application images
    log "Building application images..."
    docker-compose -f docker-compose.prod.yml build --no-cache

    # Stop existing containers
    log "Stopping existing containers..."
    docker-compose -f docker-compose.prod.yml down

    # Start new containers
    log "Starting new containers..."
    docker-compose -f docker-compose.prod.yml up -d

    # Wait for services to be ready
    log "Waiting for services to be ready..."
    sleep 30

    # Run database migrations
    log "Running database migrations..."
    docker-compose -f docker-compose.prod.yml exec -T backend npx prisma migrate deploy || error "Database migration failed"

    # Generate Prisma client
    log "Generating Prisma client..."
    docker-compose -f docker-compose.prod.yml exec -T backend npx prisma generate || error "Prisma client generation failed"

    success "Deployment completed successfully"
}

# Health check
health_check() {
    log "Performing health checks..."

    # Check backend health
    for i in {1..30}; do
        if curl -f http://localhost:3001/health &> /dev/null; then
            success "Backend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            error "Backend health check failed"
        fi
        log "Waiting for backend to be ready... ($i/30)"
        sleep 10
    done

    # Check frontend health
    for i in {1..30}; do
        if curl -f http://localhost:3000 &> /dev/null; then
            success "Frontend is healthy"
            break
        fi
        if [ $i -eq 30 ]; then
            error "Frontend health check failed"
        fi
        log "Waiting for frontend to be ready... ($i/30)"
        sleep 10
    done

    # Check database connection
    if docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U postgres &> /dev/null; then
        success "Database is healthy"
    else
        error "Database health check failed"
    fi

    success "All health checks passed"
}

# Show deployment status
show_status() {
    log "Deployment Status:"
    echo ""
    docker-compose -f docker-compose.prod.yml ps
    echo ""
    log "Application URLs:"
    echo "  Frontend: http://localhost:3000"
    echo "  Backend API: http://localhost:3001"
    echo "  Health Check: http://localhost:3001/health"
    echo ""
    log "Logs can be viewed with:"
    echo "  docker-compose -f docker-compose.prod.yml logs -f [service_name]"
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old Docker images and containers..."
    docker system prune -f
    docker image prune -f
    success "Cleanup completed"
}

# Main deployment process
main() {
    log "Starting University Schedule Assistant deployment..."

    create_directories
    check_prerequisites
    backup_data
    deploy
    health_check
    show_status
    cleanup

    success "Deployment completed successfully!"
    log "Check the application at http://localhost:3000"
}

# Run main function
main "$@"