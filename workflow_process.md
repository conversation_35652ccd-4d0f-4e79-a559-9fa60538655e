# University Schedule Assistant - Workflow Process

## Overview

The University Schedule Assistant follows a structured, step-by-step workflow that ensures data integrity, provides validation checkpoints, and allows for manual review before automated schedule generation. This workflow is inspired by aSc TimeTables and designed to handle complex university scheduling requirements.

## Complete Workflow Diagram

```mermaid
graph TD
    A[Start New Semester] --> B[Data Input Phase]
    B --> C{Data Complete?}
    C -->|No| B
    C -->|Yes| D[Generate Scheduling Manifest]
    D --> E[Download & Review Manifest]
    E --> F{Manifest Correct?}
    F -->|No| G[Edit Manifest Offline]
    G --> H[Upload Modified Manifest]
    H --> I[Validate Modified Manifest]
    I --> J{Validation Passed?}
    J -->|No| G
    J -->|Yes| K[Start Schedule Generation]
    F -->|Yes| K
    K --> L[Monitor Generation Progress]
    L --> M{Generation Complete?}
    M -->|No| L
    M -->|Yes| N[Review Generated Schedule]
    N --> O{Schedule Acceptable?}
    O -->|No| P[Manual Adjustments]
    P --> Q[Re-run Optimization]
    Q --> N
    O -->|Yes| R[Publish Schedule]
    R --> S[Daily Operations]
    S --> T[Substitution Management]
    T --> U[Generate Reports]
    U --> V[End of Semester]
```

---

## Phase 1: Data Aggregation & Input

### 1.1 Initial Setup
**Objective**: Establish the foundational data for schedule generation

**Steps**:
1. **Create New Semester**
   - Navigate to Semester Management
   - Click "Create New Semester"
   - Enter semester details (name, start date, end date)
   - Set as active semester

2. **Department Setup**
   - Verify all departments are created
   - Assign department heads
   - Update department information if needed

**User Interface**:
- Dashboard with semester overview
- Quick action buttons for common tasks
- Progress indicators showing completion status

### 1.2 Core Data Entry Methods

#### Method A: Manual UI Entry
**Best for**: Small datasets, new data, or corrections

**Process**:
1. **Instructors**
   - Navigate to Instructors → Add New
   - Fill instructor details form
   - Set availability using interactive time grid
   - Define preferred time slots
   - Set maximum daily workload

2. **Rooms**
   - Navigate to Rooms → Add New
   - Enter room details (number, capacity, type)
   - Assign to department if specialized
   - Add equipment notes

3. **Student Levels & Groups**
   - Create student levels (e.g., "First Year Pharmacy")
   - Define expected student count
   - Create groups and sub-groups with hierarchy
   - Set actual student counts per group

4. **Courses**
   - Add course information (code, title, credits)
   - Assign to department
   - Add course description

5. **Lessons**
   - Create lesson instances for each course-group combination
   - Assign instructors to lessons
   - Set lesson type and duration
   - Define required room type
   - Set priority and scheduling preferences

#### Method B: Bulk Excel Upload
**Best for**: Large datasets, data migration, or bulk updates

**Process**:
1. **Download Templates**
   - Navigate to Bulk Import section
   - Download Excel templates for each entity type
   - Templates include validation rules and examples

2. **Prepare Data**
   - Fill templates with institutional data
   - Follow naming conventions and data formats
   - Use provided lookup values for enums

3. **Upload Files**
   - Upload each completed template
   - System validates data in real-time
   - Review validation reports
   - Fix errors and re-upload if needed

4. **Confirm Import**
   - Review import summary
   - Confirm data import
   - System creates all entities and relationships

### 1.3 Data Validation & Quality Checks

**Automatic Validations**:
- Instructor availability conflicts
- Room capacity vs. group size mismatches
- Missing required relationships
- Data format and constraint violations

**Manual Review Points**:
- Instructor workload distribution
- Room utilization balance
- Course-instructor assignments
- Group size reasonableness

---

## Phase 2: Scheduling Manifest Generation

### 2.1 Manifest Generation Process
**Objective**: Create a comprehensive, reviewable document of all scheduling requirements

**Steps**:
1. **Trigger Generation**
   - Navigate to Schedule Generation
   - Select active semester
   - Click "Generate Scheduling Manifest"
   - System processes all data and creates manifest

2. **Manifest Creation**
   - System generates all required lesson instances
   - Calculates total scheduling requirements
   - Creates multi-sheet Excel workbook
   - Includes validation and reference data

### 2.2 Manifest Structure

**Primary Sheet: "Lessons to Schedule"**
```
Columns:
- Lesson ID (Auto-generated)
- Course Code
- Course Title
- Lesson Type (Lecture, Lab, Tutorial, etc.)
- Student Group
- Group Size
- Assigned Instructor
- Duration (in slots)
- Required Room Type
- Sessions Per Week
- Total Sessions
- Priority (1-10)
- Special Requirements
- Notes
```

**Reference Sheets**:
- **Instructors**: Complete instructor list with availability
- **Rooms**: All rooms with capacity and type information
- **Student Groups**: Group hierarchy and sizes
- **Courses**: Course catalog with details
- **Time Slots**: Time slot reference (1-26 mapping)
- **Constraints**: Active scheduling constraints

### 2.3 Manifest Download & Review

**Download Process**:
- System generates Excel file (typically 5-15 MB)
- File includes data validation rules
- Download link provided with expiration time
- File stored temporarily for re-download

**Review Guidelines**:
- Verify lesson count matches expectations
- Check instructor assignments
- Validate group assignments
- Review special requirements
- Confirm priority settings

---

## Phase 3: Validation & Modification

### 3.1 Path A: Direct Approval
**When to Use**: Manifest is correct and complete
- Review downloaded manifest
- Confirm all data is accurate
- Return to web interface and click "Proceed with Generation"

### 3.2 Path B: Manifest Modification
**When to Use**: Changes needed before generation
- Edit Excel file offline (modify "Lessons to Schedule" sheet only)
- Upload modified manifest via web interface
- System validates changes and provides validation report
- Fix any errors and re-upload if needed

---

## Phase 4: Automated Schedule Generation

### 4.1 Generation Process
**Four-Phase Algorithm**:
1. **Data Preparation** (5-10%): Load lessons, build availability matrices
2. **Initial Placement** (60-70%): Constraint satisfaction with backtracking
3. **Optimization** (20-30%): Local search algorithms, quality improvement
4. **Validation** (5-10%): Final checks, conflict reports, quality scoring

### 4.2 Real-time Monitoring
- Progress percentage and phase indicators
- Live statistics (lessons scheduled, conflicts, time remaining)
- Ability to cancel generation if needed

---

## Phase 5: Schedule Review & Manual Adjustments

### 5.1 Review Tools
- **Grid View**: Weekly schedule overview
- **Resource Views**: Individual instructor, room, and group schedules
- **Conflict View**: All detected conflicts with resolution suggestions

### 5.2 Manual Adjustment Features
- **Drag & Drop**: Move lessons between time slots and rooms
- **Bulk Operations**: Move entire instructor schedules, block time slots
- **Conflict Resolution**: Automatic suggestions and constraint override options
- **Re-optimization**: Lock manual changes and optimize remaining lessons

---

## Phase 6: Schedule Publication

### 6.1 Publication Process
- Final validation and quality assurance
- Multi-format generation (PDF, Excel, iCal, web)
- Stakeholder distribution via email and web portal
- Activation of public schedule access

### 6.2 Post-Publication
- System performance monitoring
- User support and change request handling
- Schedule backup and version control

---

## Phase 7: Daily Operations & Substitution Management

### 7.1 Daily Workflow
**Morning Routine**:
- System health check and error log review
- Process overnight substitution requests
- Generate and distribute daily bulletin

### 7.2 Substitution Process
1. **Request Creation**: Instructor absence reported
2. **Substitute Finding**: System suggests available qualified instructors
3. **Approval Workflow**: Department head review and approval
4. **Notification**: Automatic alerts to substitute and affected students
5. **Bulletin Update**: Daily substitution bulletin generation

### 7.3 Exception Handling
- Emergency substitution procedures
- Room unavailability protocols
- Equipment failure responses
- Escalation procedures for complex issues

---

## Phase 8: Reporting & Analytics

### 8.1 Regular Reports
- **Daily**: Substitution summary, room utilization, issue logs
- **Weekly**: Schedule adherence, conflict resolution, user feedback
- **Monthly**: Performance trends, satisfaction scores, improvement recommendations

### 8.2 Analytics Dashboard
- Real-time metrics (generation success, conflict resolution time)
- Visualizations (utilization heat maps, workload distribution)
- Quality trends and performance indicators

---

## Phase 9: End of Semester Preparation

### 9.1 Semester Closure
- Data archival and backup
- Performance analysis and stakeholder feedback
- Documentation of lessons learned

### 9.2 Next Semester Preparation
- Data rollover to new semester
- System updates and feature enhancements
- User training and documentation updates

---

## User Roles & Responsibilities

### System Administrator
- Overall system management and configuration
- User account management and permissions
- Performance monitoring and maintenance

### Academic Scheduler
- Schedule generation oversight and quality assurance
- Constraint management and stakeholder communication
- Process improvement and optimization

### Department Coordinator
- Department-specific data management
- Instructor coordination and substitution approval
- Local issue resolution and communication

### Instructor
- Availability management and schedule viewing
- Substitution request submission
- Feedback provision and system usage

### Student
- Schedule viewing and mobile access
- Notification receipt and feedback provision

---

## Success Metrics & KPIs

### Operational Excellence
- **Schedule Generation Success Rate**: >95%
- **Generation Time**: <30 minutes for 500+ lessons
- **System Uptime**: >99.5%
- **Conflict Resolution Time**: <24 hours average

### Quality Assurance
- **Schedule Quality Score**: >85/100
- **User Satisfaction**: >4.0/5.0 (instructors and students)
- **Room Utilization**: 70-85% optimal range
- **Manual Adjustment Rate**: <10% of total lessons

### Efficiency Gains
- **Data Entry Time Reduction**: >50% vs. manual methods
- **Substitution Success Rate**: >90%
- **Report Generation Time**: <5 minutes for standard reports
- **Process Automation**: >80% of routine tasks automated

---

## Implementation Timeline

### Phase 1: Foundation (Months 1-3)
- Database setup and core data models
- Basic CRUD operations and user management
- Excel import/export functionality

### Phase 2: Core Scheduling (Months 4-6)
- Scheduling engine development
- Constraint management system
- Manual adjustment tools

### Phase 3: Advanced Features (Months 7-9)
- Substitution management system
- Real-time updates and notifications
- Reporting and analytics dashboard

### Phase 4: Polish & Launch (Months 10-12)
- User interface refinement
- Performance optimization
- User training and documentation
- Production deployment and monitoring

This comprehensive workflow ensures a systematic approach to university schedule management, from initial data input through daily operations, providing the flexibility and control needed for complex academic scheduling requirements.