{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "paths": {"@/*": ["*"], "@/types/*": ["types/*"], "@/utils/*": ["utils/*"], "@/services/*": ["services/*"], "@/controllers/*": ["controllers/*"], "@/middleware/*": ["middleware/*"], "@/models/*": ["models/*"], "@/config/*": ["config/*"]}}, "include": ["src/**/*", "prisma/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}