import { Response } from 'express';
import { ApiResponse, PaginationInfo } from '../types/api';
import { logger } from '../config/logger';

/**
 * Utility functions for standardized API responses
 */

export const sendSuccess = <T>(
  res: Response,
  data?: T,
  message?: string,
  pagination?: PaginationInfo
): void => {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    pagination,
  };

  res.status(200).json(response);
};

export const sendCreated = <T>(
  res: Response,
  data?: T,
  message?: string
): void => {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message: message || 'Resource created successfully',
  };

  res.status(201).json(response);
};

export const sendNoContent = (res: Response, message?: string): void => {
  const response: ApiResponse = {
    success: true,
    message: message || 'Operation completed successfully',
  };

  res.status(204).json(response);
};

export const sendBadRequest = (
  res: Response,
  message?: string,
  errors?: string[]
): void => {
  const response: ApiResponse = {
    success: false,
    message: message || 'Bad request',
    errors,
  };

  logger.warn('Bad Request', { message, errors });
  res.status(400).json(response);
};

export const sendUnauthorized = (
  res: Response,
  message?: string
): void => {
  const response: ApiResponse = {
    success: false,
    message: message || 'Unauthorized access',
  };

  logger.warn('Unauthorized Access', { message });
  res.status(401).json(response);
};

export const sendForbidden = (
  res: Response,
  message?: string
): void => {
  const response: ApiResponse = {
    success: false,
    message: message || 'Forbidden access',
  };

  logger.warn('Forbidden Access', { message });
  res.status(403).json(response);
};

export const sendNotFound = (
  res: Response,
  message?: string
): void => {
  const response: ApiResponse = {
    success: false,
    message: message || 'Resource not found',
  };

  logger.warn('Resource Not Found', { message });
  res.status(404).json(response);
};

export const sendConflict = (
  res: Response,
  message?: string,
  errors?: string[]
): void => {
  const response: ApiResponse = {
    success: false,
    message: message || 'Resource conflict',
    errors,
  };

  logger.warn('Resource Conflict', { message, errors });
  res.status(409).json(response);
};

export const sendUnprocessableEntity = (
  res: Response,
  message?: string,
  errors?: string[]
): void => {
  const response: ApiResponse = {
    success: false,
    message: message || 'Validation failed',
    errors,
  };

  logger.warn('Validation Failed', { message, errors });
  res.status(422).json(response);
};

export const sendInternalServerError = (
  res: Response,
  message?: string,
  error?: any
): void => {
  const response: ApiResponse = {
    success: false,
    message: message || 'Internal server error',
  };

  logger.error('Internal Server Error', { message, error });
  res.status(500).json(response);
};

/**
 * Calculate pagination info
 */
export const calculatePagination = (
  total: number,
  page: number = 1,
  limit: number = 20
): PaginationInfo => {
  const totalPages = Math.ceil(total / limit);

  return {
    page: Math.max(1, page),
    limit: Math.max(1, limit),
    total,
    totalPages: Math.max(1, totalPages),
  };
};

/**
 * Parse pagination query parameters
 */
export const parsePaginationQuery = (query: any) => {
  const page = parseInt(query.page) || 1;
  const limit = Math.min(parseInt(query.limit) || 20, 100); // Max 100 items per page
  const skip = (page - 1) * limit;

  return {
    page: Math.max(1, page),
    limit: Math.max(1, limit),
    skip: Math.max(0, skip),
  };
};

/**
 * Handle async route errors
 */
export const asyncHandler = (fn: Function) => {
  return (req: any, res: any, next: any) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};