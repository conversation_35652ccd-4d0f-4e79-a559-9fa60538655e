import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import multer from 'multer';
import { ExcelService } from '../services/excelService';
import { z } from 'zod';

const router = Router();
const prisma = new PrismaClient();
const excelService = new ExcelService(prisma);

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept only Excel files
    if (file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.mimetype === 'application/vnd.ms-excel') {
      cb(null, true);
    } else {
      cb(new Error('Only Excel files are allowed'));
    }
  }
});

// Validation schemas
const importSchema = z.object({
  type: z.enum(['departments', 'rooms', 'instructors', 'courses']),
  semesterID: z.string().uuid().optional()
});

const exportSchema = z.object({
  type: z.enum(['schedule', 'departments', 'rooms', 'instructors', 'courses', 'template']),
  semesterID: z.string().uuid().optional()
});

/**
 * POST /api/excel/import
 * Import data from Excel file
 */
router.post('/import', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    const { type, semesterID } = importSchema.parse(req.body);

    // Validate Excel structure first
    const validation = await excelService.validateExcelStructure(req.file.buffer, type);
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid Excel file structure',
        details: validation.errors
      });
    }

    let result;
    switch (type) {
      case 'departments':
        result = await excelService.importDepartments(req.file.buffer);
        break;
      case 'rooms':
        result = await excelService.importRooms(req.file.buffer);
        break;
      case 'instructors':
        result = await excelService.importInstructors(req.file.buffer);
        break;
      case 'courses':
        if (!semesterID) {
          return res.status(400).json({
            success: false,
            error: 'Semester ID is required for course import'
          });
        }
        result = await excelService.importCourses(req.file.buffer, semesterID);
        break;
      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid import type'
        });
    }

    res.json({
      success: result.success,
      data: {
        importedCount: result.importedCount,
        errors: result.errors,
        warnings: result.warnings
      },
      message: result.message
    });

  } catch (error) {
    console.error('Error importing Excel file:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    if (error instanceof multer.MulterError) {
      return res.status(400).json({
        success: false,
        error: 'File upload error',
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to import Excel file',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/excel/export
 * Export data to Excel file
 */
router.get('/export', async (req, res) => {
  try {
    const { type, semesterID } = exportSchema.parse(req.query);

    let exportData;
    switch (type) {
      case 'schedule':
        if (!semesterID) {
          return res.status(400).json({
            success: false,
            error: 'Semester ID is required for schedule export'
          });
        }
        exportData = await excelService.exportSchedule(semesterID);
        break;
      case 'departments':
        exportData = await excelService.exportDepartmentsTemplate();
        break;
      case 'rooms':
        exportData = await excelService.exportRoomsTemplate();
        break;
      case 'instructors':
        exportData = await excelService.exportInstructorsTemplate();
        break;
      case 'courses':
        if (!semesterID) {
          return res.status(400).json({
            success: false,
            error: 'Semester ID is required for courses export'
          });
        }
        exportData = await excelService.exportCoursesTemplate(semesterID);
        break;
      case 'template':
        const templateType = req.query.templateType as string;
        if (!templateType || !['departments', 'rooms', 'instructors', 'courses'].includes(templateType)) {
          return res.status(400).json({
            success: false,
            error: 'Valid template type is required (departments, rooms, instructors, courses)'
          });
        }
        exportData = await excelService.generateImportTemplate(
          templateType as 'departments' | 'rooms' | 'instructors' | 'courses',
          semesterID
        );
        break;
      default:
        return res.status(400).json({
          success: false,
          error: 'Invalid export type'
        });
    }

    // Set response headers for file download
    res.setHeader('Content-Type', exportData.mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${exportData.filename}"`);
    res.setHeader('Content-Length', exportData.buffer.length);

    res.send(exportData.buffer);

  } catch (error) {
    console.error('Error exporting Excel file:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to export Excel file',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/excel/validate
 * Validate Excel file structure before import
 */
router.post('/validate', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    const { type } = z.object({
      type: z.enum(['departments', 'rooms', 'instructors', 'courses'])
    }).parse(req.body);

    const validation = await excelService.validateExcelStructure(req.file.buffer, type);

    res.json({
      success: validation.valid,
      data: {
        valid: validation.valid,
        errors: validation.errors
      },
      message: validation.valid ? 'Excel file structure is valid' : 'Excel file structure validation failed'
    });

  } catch (error) {
    console.error('Error validating Excel file:', error);

    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.errors
      });
    }

    if (error instanceof multer.MulterError) {
      return res.status(400).json({
        success: false,
        error: 'File upload error',
        message: error.message
      });
    }

    res.status(500).json({
      success: false,
      error: 'Failed to validate Excel file',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/excel/templates
 * Get list of available Excel templates
 */
router.get('/templates', async (req, res) => {
  try {
    const templates = [
      {
        type: 'departments',
        name: 'Departments Template',
        description: 'Template for importing department data',
        requiredColumns: ['Department Name', 'Department Code'],
        optionalColumns: []
      },
      {
        type: 'rooms',
        name: 'Rooms Template',
        description: 'Template for importing room data',
        requiredColumns: ['Room Name', 'Capacity'],
        optionalColumns: ['Room Type', 'Building', 'Floor', 'Equipment']
      },
      {
        type: 'instructors',
        name: 'Instructors Template',
        description: 'Template for importing instructor data',
        requiredColumns: ['First Name', 'Last Name', 'Email', 'Department Code'],
        optionalColumns: ['Phone', 'Office', 'Max Hours Per Day', 'Max Hours Per Week']
      },
      {
        type: 'courses',
        name: 'Courses Template',
        description: 'Template for importing course data',
        requiredColumns: ['Course Code', 'Course Name', 'Department Code', 'Instructor Email'],
        optionalColumns: ['Credits', 'Sessions Per Week', 'Session Duration', 'Min Room Capacity', 'Required Room Type', 'Priority']
      }
    ];

    res.json({
      success: true,
      data: templates,
      message: 'Available Excel templates retrieved successfully'
    });

  } catch (error) {
    console.error('Error getting Excel templates:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get Excel templates',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/excel/sample/:type
 * Download sample Excel file for a specific type
 */
router.get('/sample/:type', async (req, res) => {
  try {
    const type = req.params.type as 'departments' | 'rooms' | 'instructors' | 'courses';
    const semesterID = req.query.semesterID as string;

    if (!['departments', 'rooms', 'instructors', 'courses'].includes(type)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid template type'
      });
    }

    if (type === 'courses' && !semesterID) {
      return res.status(400).json({
        success: false,
        error: 'Semester ID is required for courses template'
      });
    }

    const exportData = await excelService.generateImportTemplate(type, semesterID);

    // Set response headers for file download
    res.setHeader('Content-Type', exportData.mimeType);
    res.setHeader('Content-Disposition', `attachment; filename="${exportData.filename}"`);
    res.setHeader('Content-Length', exportData.buffer.length);

    res.send(exportData.buffer);

  } catch (error) {
    console.error('Error generating sample Excel file:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to generate sample Excel file',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;