import { Department } from '@prisma/client';
import prisma from '../config/database';
import { CreateDepartmentDTO, UpdateDepartmentDTO, DepartmentFilters } from '../types/api';
import { logger } from '../config/logger';

/**
 * Department Service - Handles all department-related business logic
 */
export class DepartmentService {
  /**
   * Get all departments with optional filtering and pagination
   */
  static async getAllDepartments(filters: DepartmentFilters) {
    const { page = 1, limit = 20, search } = filters;
    const skip = (page - 1) * limit;

    const where: any = {};
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
      ];
    }

    const [departments, total] = await Promise.all([
      prisma.department.findMany({
        where,
        skip,
        take: limit,
        orderBy: { name: 'asc' },
        include: {
          _count: {
            select: {
              instructors: true,
              courses: true,
              studentLevels: true,
              rooms: true,
            },
          },
        },
      }),
      prisma.department.count({ where }),
    ]);

    logger.info('Retrieved departments', {
      count: departments.length,
      total,
      filters
    });

    return { departments, total };
  }

  /**
   * Get department by ID
   */
  static async getDepartmentById(departmentID: string): Promise<Department | null> {
    const department = await prisma.department.findUnique({
      where: { departmentID },
      include: {
        instructors: {
          select: {
            instructorID: true,
            name: true,
            email: true,
            maxDailySlots: true,
          },
        },
        courses: {
          select: {
            courseID: true,
            courseCode: true,
            title: true,
            credits: true,
          },
        },
        studentLevels: {
          select: {
            levelID: true,
            name: true,
            expectedStudentCount: true,
            yearNumber: true,
            semesterNumber: true,
          },
        },
        rooms: {
          select: {
            roomID: true,
            roomNumber: true,
            maxCapacity: true,
            type: true,
            building: true,
          },
        },
        _count: {
          select: {
            instructors: true,
            courses: true,
            studentLevels: true,
            rooms: true,
          },
        },
      },
    });

    if (!department) {
      logger.warn('Department not found', { departmentID });
      return null;
    }

    return department;
  }

  /**
   * Create new department
   */
  static async createDepartment(data: CreateDepartmentDTO): Promise<Department> {
    const department = await prisma.department.create({
      data: {
        name: data.name,
        code: data.code,
      },
    });

    logger.info('Created department', {
      departmentID: department.departmentID,
      name: department.name
    });

    return department;
  }

  /**
   * Update department
   */
  static async updateDepartment(
    departmentID: string,
    data: UpdateDepartmentDTO
  ): Promise<Department | null> {
    // Check if department exists
    const existingDepartment = await this.getDepartmentById(departmentID);
    if (!existingDepartment) {
      return null;
    }

    const updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.code !== undefined) updateData.code = data.code;

    const department = await prisma.department.update({
      where: { departmentID },
      data: updateData,
    });

    logger.info('Updated department', {
      departmentID,
      changes: Object.keys(updateData)
    });

    return department;
  }

  /**
   * Delete department
   */
  static async deleteDepartment(departmentID: string): Promise<boolean> {
    try {
      // Check if department has any associated data
      const [instructorsCount, coursesCount, studentLevelsCount, roomsCount, usersCount] = await Promise.all([
        prisma.instructor.count({ where: { departmentID } }),
        prisma.course.count({ where: { departmentID } }),
        prisma.studentLevel.count({ where: { departmentID } }),
        prisma.room.count({ where: { assignedDepartmentID: departmentID } }),
        prisma.user.count({ where: { departmentID } }),
      ]);

      if (instructorsCount > 0 || coursesCount > 0 || studentLevelsCount > 0 || roomsCount > 0 || usersCount > 0) {
        logger.warn('Cannot delete department with associated data', {
          departmentID,
          instructorsCount,
          coursesCount,
          studentLevelsCount,
          roomsCount,
          usersCount,
        });
        throw new Error('Cannot delete department with associated instructors, courses, student levels, rooms, or users');
      }

      await prisma.department.delete({
        where: { departmentID },
      });

      logger.info('Deleted department', { departmentID });
      return true;
    } catch (error) {
      logger.error('Failed to delete department', { departmentID, error });
      throw error;
    }
  }

  /**
   * Get department statistics
   */
  static async getDepartmentStatistics(departmentID: string) {
    const department = await prisma.department.findUnique({
      where: { departmentID },
      include: {
        _count: {
          select: {
            instructors: true,
            courses: true,
            studentLevels: true,
            rooms: true,
          },
        },
      },
    });

    if (!department) {
      return null;
    }

    // Get additional statistics
    const [totalStudents, activeLessons, scheduledLessons] = await Promise.all([
      prisma.studentLevel.aggregate({
        where: { departmentID },
        _sum: { expectedStudentCount: true },
      }),
      prisma.lesson.count({
        where: {
          course: { departmentID },
        },
      }),
      prisma.scheduledLesson.count({
        where: {
          lesson: {
            course: { departmentID },
          },
        },
      }),
    ]);

    return {
      departmentID: department.departmentID,
      name: department.name,
      code: department.code,
      statistics: {
        instructorsCount: department._count.instructors,
        coursesCount: department._count.courses,
        studentLevelsCount: department._count.studentLevels,
        roomsCount: department._count.rooms,
        totalStudents: totalStudents._sum.expectedStudentCount || 0,
        activeLessons,
        scheduledLessons,
        schedulingRate: activeLessons > 0 ? (scheduledLessons / activeLessons) * 100 : 0,
      },
    };
  }

  /**
   * Check if department name is unique
   */
  static async isDepartmentNameUnique(name: string, excludeId?: string): Promise<boolean> {
    const where: any = { name };
    if (excludeId) {
      where.departmentID = { not: excludeId };
    }

    const existingDepartment = await prisma.department.findFirst({ where });
    return !existingDepartment;
  }

  /**
   * Check if department code is unique
   */
  static async isDepartmentCodeUnique(code: string, excludeId?: string): Promise<boolean> {
    if (!code) return true; // Code is optional

    const where: any = { code };
    if (excludeId) {
      where.departmentID = { not: excludeId };
    }

    const existingDepartment = await prisma.department.findFirst({ where });
    return !existingDepartment;
  }

  /**
   * Get departments with minimal data for dropdowns
   */
  static async getDepartmentsForDropdown() {
    const departments = await prisma.department.findMany({
      select: {
        departmentID: true,
        name: true,
        code: true,
      },
      orderBy: { name: 'asc' },
    });

    return departments;
  }
}