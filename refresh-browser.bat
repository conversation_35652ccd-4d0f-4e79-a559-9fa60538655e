@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Browser Refresh Script
:: This script clears browser cache and opens fresh application instance

echo ========================================
echo University Schedule Assistant - Refresh
echo ========================================
echo.

:: Set color for better visibility
color 0D

:: Configuration
set "FRONTEND_PORT=3000"

echo [INFO] Refreshing University Schedule Assistant...
echo.

:: Clear browser cache
echo [INFO] Clearing browser cache...
powershell -command "Start-Process -FilePath 'rundll32.exe' -ArgumentList 'InetCpl.cpl,ClearMyTracksByProcess 255' -Wait" >nul 2>&1
echo [SUCCESS] Browser cache cleared
echo.

:: Wait a moment
timeout /t 2 /nobreak >nul

:: Check if application is running
echo [INFO] Checking if application is running...
powershell -command "try { $response = Invoke-WebRequest -Uri 'http://localhost:%FRONTEND_PORT%' -TimeoutSec 5 -UseBasicParsing; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Application is not running
    echo [INFO] Please start the application first using: start-application.bat
    pause
    exit /b 1
)
echo [SUCCESS] Application is running
echo.

:: Open browser with cache-busting parameters
echo [INFO] Opening fresh application instance...
echo [INFO] This will open with cleared cache to show the latest version
start "University Schedule Assistant" "http://localhost:%FRONTEND_PORT%?t=%random%&cache=false"

echo.
echo [SUCCESS] Fresh application instance opened!
echo [INFO] If you still see an old version, press Ctrl+F5 to force refresh
pause