@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Debug Startup Script
:: This script provides detailed debugging information

echo ========================================
echo University Schedule Assistant - Debug
echo ========================================
echo.

:: Set color for better visibility
color 0E

echo [INFO] Debugging University Schedule Assistant...
echo.

:: Check Docker status
echo [INFO] Docker Status:
docker --version
docker-compose --version
docker info
echo.

:: Check if .env file exists and show contents
echo [INFO] Environment Configuration:
if exist ".env" (
    echo [SUCCESS] .env file found
    echo [INFO] .env file contents:
    type .env
) else (
    echo [ERROR] .env file not found
)
echo.

:: Check current containers
echo [INFO] Current Containers:
docker ps -a
echo.

:: Check Docker Compose configuration
echo [INFO] Docker Compose Configuration:
docker-compose config
echo.

:: Check if ports are in use
echo [INFO] Port Usage Check:
netstat -an | findstr ":3000 :3001 :5432 :6379"
echo.

:: Check if files exist
echo [INFO] File Existence Check:
if exist "Dockerfile.dev" (
    echo [SUCCESS] Dockerfile.dev exists
) else (
    echo [ERROR] Dockerfile.dev missing
)

if exist "frontend\Dockerfile.dev" (
    echo [SUCCESS] frontend\Dockerfile.dev exists
) else (
    echo [ERROR] frontend\Dockerfile.dev missing
)

if exist "package.json" (
    echo [SUCCESS] package.json exists
) else (
    echo [ERROR] package.json missing
)

if exist "frontend\package.json" (
    echo [SUCCESS] frontend\package.json exists
) else (
    echo [ERROR] frontend\package.json missing
)

if exist "prisma\schema.prisma" (
    echo [SUCCESS] prisma\schema.prisma exists
) else (
    echo [ERROR] prisma\schema.prisma missing
)
echo.

:: Try to start just the database
echo [INFO] Testing database startup...
docker-compose up -d postgres
timeout /t 10 /nobreak >nul
docker-compose ps postgres
docker-compose logs postgres
echo.

echo [INFO] Debug information collected!
echo [INFO] Check the output above for any issues
pause