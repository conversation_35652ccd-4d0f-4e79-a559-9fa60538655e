# University Schedule Assistant - Testing Suite

## Overview
This directory contains comprehensive testing tools and documentation for the University Schedule Assistant system. The testing suite covers functional testing, performance testing, security testing, and user acceptance testing.

## Testing Structure

```
testing/
├── README.md                    # This file
├── user-testing-guide.md       # Comprehensive user testing scenarios
├── generate-test-data.js        # Test data generation script
├── performance-tests.js         # Performance and load testing
├── security-tests.js           # Security vulnerability testing
├── test-data/                  # Generated test data files
│   ├── departments_valid.xlsx
│   ├── rooms_valid.xlsx
│   ├── instructors_valid.xlsx
│   ├── courses_valid.xlsx
│   ├── departments_invalid.xlsx
│   └── rooms_invalid.xlsx
└── reports/                    # Test execution reports
    ├── performance-report.json
    └── security-report.json
```

## Quick Start

### Prerequisites
```bash
# Install testing dependencies
npm install axios xlsx

# Ensure the application is running
# Backend: http://localhost:3001
# Frontend: http://localhost:3000
```

### Generate Test Data
```bash
# Generate Excel test files
cd testing
node generate-test-data.js
```

### Run Performance Tests
```bash
# Basic performance test
node performance-tests.js

# Custom configuration
CONCURRENT_USERS=20 TEST_DURATION=120000 node performance-tests.js
```

### Run Security Tests
```bash
# Security vulnerability scan
node security-tests.js

# Custom target
TEST_BASE_URL=https://your-domain.com node security-tests.js
```

## Testing Types

### 1. Functional Testing
**Purpose**: Verify all features work as expected
**Coverage**:
- CRUD operations for all entities
- Schedule generation
- Excel import/export
- User interface interactions
- API endpoints

**How to Run**: Follow the scenarios in `user-testing-guide.md`

### 2. Performance Testing
**Purpose**: Ensure system performs well under load
**Coverage**:
- Load testing (concurrent users)
- Stress testing (increasing load)
- Database performance
- Memory leak detection

**Configuration**:
```bash
# Environment variables
TEST_BASE_URL=http://localhost:3001
CONCURRENT_USERS=10
TEST_DURATION=60000
RAMP_UP_TIME=10000
```

**Metrics Collected**:
- Response times (average, min, max, percentiles)
- Throughput (requests per second)
- Error rates
- Resource utilization

### 3. Security Testing
**Purpose**: Identify security vulnerabilities
**Coverage**:
- SQL injection attacks
- Cross-site scripting (XSS)
- Cross-site request forgery (CSRF)
- File upload security
- Input validation
- Security headers
- Rate limiting
- Authentication security
- Information disclosure

**Security Score**: Calculated based on passed/failed tests

### 4. User Acceptance Testing
**Purpose**: Validate system meets user requirements
**Coverage**:
- End-to-end workflows
- Real-world scenarios
- Usability testing
- Accessibility testing
- Browser compatibility

## Test Data

### Valid Test Data
- **Departments**: 10 sample departments with realistic names and codes
- **Rooms**: 10 rooms with various capacities, types, and equipment
- **Instructors**: 10 instructors with complete profiles and constraints
- **Courses**: 10 courses with realistic scheduling requirements

### Invalid Test Data
- **Departments**: Missing fields, duplicates, invalid formats
- **Rooms**: Invalid capacities, missing required fields
- **Files**: Malicious files, oversized files, wrong formats

### Large Test Data
- **Departments**: 100 departments for performance testing
- **Rooms**: 500 rooms for stress testing
- **Bulk Operations**: Large datasets for import/export testing

## Test Execution

### Manual Testing
1. Follow the step-by-step guide in `user-testing-guide.md`
2. Use the provided test data files
3. Document results using the bug report template
4. Complete the testing checklist

### Automated Testing
```bash
# Run all automated tests
npm run test:all

# Individual test suites
npm run test:performance
npm run test:security
npm run test:functional
```

### Continuous Integration
```yaml
# Example CI configuration
test:
  script:
    - npm install
    - npm run build
    - npm run test:performance
    - npm run test:security
  artifacts:
    reports:
      - testing/reports/
```

## Test Reports

### Performance Report
```json
{
  "summary": {
    "totalRequests": 1000,
    "successRate": "99.5%",
    "averageResponseTime": "150ms",
    "throughput": "50 req/s"
  },
  "percentiles": {
    "p50": "120ms",
    "p90": "250ms",
    "p95": "350ms",
    "p99": "500ms"
  }
}
```

### Security Report
```json
{
  "summary": {
    "totalTests": 50,
    "passedTests": 45,
    "failedTests": 5,
    "securityScore": "75/100"
  },
  "vulnerabilities": [
    {
      "test": "XSS - Department Name",
      "details": "Payload not properly escaped"
    }
  ],
  "recommendations": [
    "Implement proper output encoding",
    "Configure Content Security Policy"
  ]
}
```