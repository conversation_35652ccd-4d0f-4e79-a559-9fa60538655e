import * as XLSX from 'xlsx';
import { PrismaClient } from '@prisma/client';
import { logger } from '../config/logger';

export interface ExcelImportResult {
  success: boolean;
  message: string;
  importedCount: number;
  errors: string[];
  warnings: string[];
}

export interface ExcelExportData {
  filename: string;
  buffer: Buffer;
  mimeType: string;
}

export class ExcelService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Import departments from Excel file
   */
  async importDepartments(buffer: Buffer): Promise<ExcelImportResult> {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const data = XLSX.utils.sheet_to_json(worksheet) as any[];

      const result: ExcelImportResult = {
        success: true,
        message: '',
        importedCount: 0,
        errors: [],
        warnings: []
      };

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowNumber = i + 2; // Excel row number (accounting for header)

        try {
          // Validate required fields
          if (!row['Department Name'] || !row['Department Code']) {
            result.errors.push(`Row ${rowNumber}: Missing required fields (Department Name, Department Code)`);
            continue;
          }

          // Check if department already exists
          const existingDepartment = await this.prisma.department.findFirst({
            where: {
              OR: [
                { departmentCode: row['Department Code'] },
                { departmentName: row['Department Name'] }
              ]
            }
          });

          if (existingDepartment) {
            result.warnings.push(`Row ${rowNumber}: Department '${row['Department Name']}' already exists, skipping`);
            continue;
          }

          // Create department
          await this.prisma.department.create({
            data: {
              departmentName: row['Department Name'].toString().trim(),
              departmentCode: row['Department Code'].toString().trim().toUpperCase(),
              createdAt: new Date(),
              updatedAt: new Date()
            }
          });

          result.importedCount++;

        } catch (error) {
          result.errors.push(`Row ${rowNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.message = `Import completed. ${result.importedCount} departments imported successfully.`;
      if (result.errors.length > 0) {
        result.success = false;
        result.message += ` ${result.errors.length} errors encountered.`;
      }

      return result;

    } catch (error) {
      logger.error('Error importing departments from Excel:', error);
      return {
        success: false,
        message: 'Failed to process Excel file',
        importedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        warnings: []
      };
    }
  }

  /**
   * Import rooms from Excel file
   */
  async importRooms(buffer: Buffer): Promise<ExcelImportResult> {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const data = XLSX.utils.sheet_to_json(worksheet) as any[];

      const result: ExcelImportResult = {
        success: true,
        message: '',
        importedCount: 0,
        errors: [],
        warnings: []
      };

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowNumber = i + 2;

        try {
          // Validate required fields
          if (!row['Room Name'] || !row['Capacity']) {
            result.errors.push(`Row ${rowNumber}: Missing required fields (Room Name, Capacity)`);
            continue;
          }

          const capacity = parseInt(row['Capacity']);
          if (isNaN(capacity) || capacity <= 0) {
            result.errors.push(`Row ${rowNumber}: Invalid capacity value`);
            continue;
          }

          // Check if room already exists
          const existingRoom = await this.prisma.room.findFirst({
            where: { roomName: row['Room Name'].toString().trim() }
          });

          if (existingRoom) {
            result.warnings.push(`Row ${rowNumber}: Room '${row['Room Name']}' already exists, skipping`);
            continue;
          }

          // Create room
          const roomData: any = {
            roomName: row['Room Name'].toString().trim(),
            capacity: capacity,
            roomType: row['Room Type'] ? row['Room Type'].toString().trim() : 'Standard',
            createdAt: new Date(),
            updatedAt: new Date()
          };

          // Add optional fields
          if (row['Building']) {
            roomData.building = row['Building'].toString().trim();
          }
          if (row['Floor']) {
            roomData.floor = row['Floor'].toString().trim();
          }

          const room = await this.prisma.room.create({ data: roomData });

          // Handle equipment if provided
          if (row['Equipment']) {
            const equipmentList = row['Equipment'].toString().split(',').map((eq: string) => eq.trim());
            for (const equipmentName of equipmentList) {
              if (equipmentName) {
                await this.prisma.equipment.create({
                  data: {
                    roomID: room.roomID,
                    equipmentName: equipmentName,
                    createdAt: new Date(),
                    updatedAt: new Date()
                  }
                });
              }
            }
          }

          result.importedCount++;

        } catch (error) {
          result.errors.push(`Row ${rowNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.message = `Import completed. ${result.importedCount} rooms imported successfully.`;
      if (result.errors.length > 0) {
        result.success = false;
        result.message += ` ${result.errors.length} errors encountered.`;
      }

      return result;

    } catch (error) {
      logger.error('Error importing rooms from Excel:', error);
      return {
        success: false,
        message: 'Failed to process Excel file',
        importedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        warnings: []
      };
    }
  }

  /**
   * Import instructors from Excel file
   */
  async importInstructors(buffer: Buffer): Promise<ExcelImportResult> {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const data = XLSX.utils.sheet_to_json(worksheet) as any[];

      const result: ExcelImportResult = {
        success: true,
        message: '',
        importedCount: 0,
        errors: [],
        warnings: []
      };

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowNumber = i + 2;

        try {
          // Validate required fields
          if (!row['First Name'] || !row['Last Name'] || !row['Email'] || !row['Department Code']) {
            result.errors.push(`Row ${rowNumber}: Missing required fields (First Name, Last Name, Email, Department Code)`);
            continue;
          }

          // Find department
          const department = await this.prisma.department.findFirst({
            where: { departmentCode: row['Department Code'].toString().trim().toUpperCase() }
          });

          if (!department) {
            result.errors.push(`Row ${rowNumber}: Department '${row['Department Code']}' not found`);
            continue;
          }

          // Check if instructor already exists
          const existingInstructor = await this.prisma.instructor.findFirst({
            where: { email: row['Email'].toString().trim().toLowerCase() }
          });

          if (existingInstructor) {
            result.warnings.push(`Row ${rowNumber}: Instructor '${row['Email']}' already exists, skipping`);
            continue;
          }

          // Create instructor
          const instructorData: any = {
            firstName: row['First Name'].toString().trim(),
            lastName: row['Last Name'].toString().trim(),
            email: row['Email'].toString().trim().toLowerCase(),
            departmentID: department.departmentID,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          // Add optional fields
          if (row['Phone']) {
            instructorData.phone = row['Phone'].toString().trim();
          }
          if (row['Office']) {
            instructorData.office = row['Office'].toString().trim();
          }
          if (row['Max Hours Per Day']) {
            const maxHours = parseInt(row['Max Hours Per Day']);
            if (!isNaN(maxHours) && maxHours > 0) {
              instructorData.maxHoursPerDay = maxHours;
            }
          }
          if (row['Max Hours Per Week']) {
            const maxHours = parseInt(row['Max Hours Per Week']);
            if (!isNaN(maxHours) && maxHours > 0) {
              instructorData.maxHoursPerWeek = maxHours;
            }
          }

          await this.prisma.instructor.create({ data: instructorData });
          result.importedCount++;

        } catch (error) {
          result.errors.push(`Row ${rowNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.message = `Import completed. ${result.importedCount} instructors imported successfully.`;
      if (result.errors.length > 0) {
        result.success = false;
        result.message += ` ${result.errors.length} errors encountered.`;
      }

      return result;

    } catch (error) {
      logger.error('Error importing instructors from Excel:', error);
      return {
        success: false,
        message: 'Failed to process Excel file',
        importedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        warnings: []
      };
    }
  }

  /**
   * Import courses from Excel file
   */
  async importCourses(buffer: Buffer, semesterID: string): Promise<ExcelImportResult> {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const data = XLSX.utils.sheet_to_json(worksheet) as any[];

      const result: ExcelImportResult = {
        success: true,
        message: '',
        importedCount: 0,
        errors: [],
        warnings: []
      };

      // Verify semester exists
      const semester = await this.prisma.semester.findUnique({
        where: { semesterID }
      });

      if (!semester) {
        return {
          success: false,
          message: 'Semester not found',
          importedCount: 0,
          errors: ['Invalid semester ID'],
          warnings: []
        };
      }

      for (let i = 0; i < data.length; i++) {
        const row = data[i];
        const rowNumber = i + 2;

        try {
          // Validate required fields
          if (!row['Course Code'] || !row['Course Name'] || !row['Department Code'] || !row['Instructor Email']) {
            result.errors.push(`Row ${rowNumber}: Missing required fields (Course Code, Course Name, Department Code, Instructor Email)`);
            continue;
          }

          // Find department
          const department = await this.prisma.department.findFirst({
            where: { departmentCode: row['Department Code'].toString().trim().toUpperCase() }
          });

          if (!department) {
            result.errors.push(`Row ${rowNumber}: Department '${row['Department Code']}' not found`);
            continue;
          }

          // Find instructor
          const instructor = await this.prisma.instructor.findFirst({
            where: { email: row['Instructor Email'].toString().trim().toLowerCase() }
          });

          if (!instructor) {
            result.errors.push(`Row ${rowNumber}: Instructor '${row['Instructor Email']}' not found`);
            continue;
          }

          // Check if course already exists in this semester
          const existingCourse = await this.prisma.course.findFirst({
            where: {
              courseCode: row['Course Code'].toString().trim().toUpperCase(),
              semesterID: semesterID
            }
          });

          if (existingCourse) {
            result.warnings.push(`Row ${rowNumber}: Course '${row['Course Code']}' already exists in this semester, skipping`);
            continue;
          }

          // Create course
          const courseData: any = {
            courseCode: row['Course Code'].toString().trim().toUpperCase(),
            courseName: row['Course Name'].toString().trim(),
            departmentID: department.departmentID,
            instructorID: instructor.instructorID,
            semesterID: semesterID,
            createdAt: new Date(),
            updatedAt: new Date()
          };

          // Add optional fields
          if (row['Credits']) {
            const credits = parseInt(row['Credits']);
            if (!isNaN(credits) && credits > 0) {
              courseData.credits = credits;
            }
          }
          if (row['Sessions Per Week']) {
            const sessions = parseInt(row['Sessions Per Week']);
            if (!isNaN(sessions) && sessions > 0) {
              courseData.sessionsPerWeek = sessions;
            }
          }
          if (row['Session Duration']) {
            const duration = parseInt(row['Session Duration']);
            if (!isNaN(duration) && duration > 0) {
              courseData.sessionDuration = duration;
            }
          }
          if (row['Min Room Capacity']) {
            const capacity = parseInt(row['Min Room Capacity']);
            if (!isNaN(capacity) && capacity > 0) {
              courseData.minRoomCapacity = capacity;
            }
          }
          if (row['Required Room Type']) {
            courseData.requiredRoomType = row['Required Room Type'].toString().trim();
          }
          if (row['Priority']) {
            courseData.priority = row['Priority'].toString().trim().toUpperCase();
          }

          await this.prisma.course.create({ data: courseData });
          result.importedCount++;

        } catch (error) {
          result.errors.push(`Row ${rowNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      result.message = `Import completed. ${result.importedCount} courses imported successfully.`;
      if (result.errors.length > 0) {
        result.success = false;
        result.message += ` ${result.errors.length} errors encountered.`;
      }

      return result;

    } catch (error) {
      logger.error('Error importing courses from Excel:', error);
      return {
        success: false,
        message: 'Failed to process Excel file',
        importedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        warnings: []
      };
    }
  }

  /**
   * Export schedule to Excel file
   */
  async exportSchedule(semesterID: string): Promise<ExcelExportData> {
    try {
      // Fetch schedule data
      const lessons = await this.prisma.lesson.findMany({
        where: {
          course: { semesterID }
        },
        include: {
          course: {
            include: {
              department: true
            }
          },
          instructor: true,
          room: true
        },
        orderBy: [
          { dayOfWeek: 'asc' },
          { startTime: 'asc' }
        ]
      });

      // Fetch semester info
      const semester = await this.prisma.semester.findUnique({
        where: { semesterID }
      });

      if (!semester) {
        throw new Error('Semester not found');
      }

      // Prepare data for Excel
      const scheduleData = lessons.map(lesson => ({
        'Course Code': lesson.course.courseCode,
        'Course Name': lesson.course.courseName,
        'Department': lesson.course.department.departmentName,
        'Instructor': `${lesson.instructor.firstName} ${lesson.instructor.lastName}`,
        'Room': lesson.room.roomName,
        'Day': lesson.dayOfWeek,
        'Start Time': lesson.startTime,
        'End Time': lesson.endTime,
        'Duration (min)': lesson.duration,
        'Session Number': lesson.sessionNumber
      }));

      // Create workbook
      const workbook = XLSX.utils.book_new();

      // Add schedule sheet
      const scheduleSheet = XLSX.utils.json_to_sheet(scheduleData);
      XLSX.utils.book_append_sheet(workbook, scheduleSheet, 'Schedule');

      // Add summary sheet
      const summaryData = await this.generateScheduleSummary(semesterID);
      const summarySheet = XLSX.utils.json_to_sheet(summaryData);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

      // Generate buffer
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      return {
        filename: `Schedule_${semester.semesterName}_${new Date().toISOString().split('T')[0]}.xlsx`,
        buffer: Buffer.from(buffer),
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };

    } catch (error) {
      logger.error('Error exporting schedule to Excel:', error);
      throw new Error(`Failed to export schedule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export departments to Excel template
   */
  async exportDepartmentsTemplate(): Promise<ExcelExportData> {
    try {
      const departments = await this.prisma.department.findMany({
        orderBy: { departmentName: 'asc' }
      });

      const data = departments.map(dept => ({
        'Department Name': dept.departmentName,
        'Department Code': dept.departmentCode
      }));

      // Add sample row if no data
      if (data.length === 0) {
        data.push({
          'Department Name': 'Computer Science',
          'Department Code': 'CS'
        });
      }

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(data);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Departments');

      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      return {
        filename: `Departments_Template_${new Date().toISOString().split('T')[0]}.xlsx`,
        buffer: Buffer.from(buffer),
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };

    } catch (error) {
      logger.error('Error exporting departments template:', error);
      throw new Error(`Failed to export departments template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export rooms to Excel template
   */
  async exportRoomsTemplate(): Promise<ExcelExportData> {
    try {
      const rooms = await this.prisma.room.findMany({
        include: {
          equipment: true
        },
        orderBy: { roomName: 'asc' }
      });

      const data = rooms.map(room => ({
        'Room Name': room.roomName,
        'Capacity': room.capacity,
        'Room Type': room.roomType,
        'Building': room.building || '',
        'Floor': room.floor || '',
        'Equipment': room.equipment.map(eq => eq.equipmentName).join(', ')
      }));

      // Add sample row if no data
      if (data.length === 0) {
        data.push({
          'Room Name': 'Room 101',
          'Capacity': 30,
          'Room Type': 'Lecture Hall',
          'Building': 'Main Building',
          'Floor': '1st Floor',
          'Equipment': 'Projector, Whiteboard, Audio System'
        });
      }

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(data);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Rooms');

      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      return {
        filename: `Rooms_Template_${new Date().toISOString().split('T')[0]}.xlsx`,
        buffer: Buffer.from(buffer),
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };

    } catch (error) {
      logger.error('Error exporting rooms template:', error);
      throw new Error(`Failed to export rooms template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export instructors to Excel template
   */
  async exportInstructorsTemplate(): Promise<ExcelExportData> {
    try {
      const instructors = await this.prisma.instructor.findMany({
        include: {
          department: true
        },
        orderBy: [
          { lastName: 'asc' },
          { firstName: 'asc' }
        ]
      });

      const data = instructors.map(instructor => ({
        'First Name': instructor.firstName,
        'Last Name': instructor.lastName,
        'Email': instructor.email,
        'Phone': instructor.phone || '',
        'Office': instructor.office || '',
        'Department Code': instructor.department.departmentCode,
        'Max Hours Per Day': instructor.maxHoursPerDay || 8,
        'Max Hours Per Week': instructor.maxHoursPerWeek || 40
      }));

      // Add sample row if no data
      if (data.length === 0) {
        data.push({
          'First Name': 'John',
          'Last Name': 'Doe',
          'Email': '<EMAIL>',
          'Phone': '******-0123',
          'Office': 'CS-201',
          'Department Code': 'CS',
          'Max Hours Per Day': 8,
          'Max Hours Per Week': 40
        });
      }

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(data);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Instructors');

      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      return {
        filename: `Instructors_Template_${new Date().toISOString().split('T')[0]}.xlsx`,
        buffer: Buffer.from(buffer),
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };

    } catch (error) {
      logger.error('Error exporting instructors template:', error);
      throw new Error(`Failed to export instructors template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export courses to Excel template
   */
  async exportCoursesTemplate(semesterID: string): Promise<ExcelExportData> {
    try {
      const courses = await this.prisma.course.findMany({
        where: { semesterID },
        include: {
          department: true,
          instructor: true
        },
        orderBy: { courseCode: 'asc' }
      });

      const semester = await this.prisma.semester.findUnique({
        where: { semesterID }
      });

      const data = courses.map(course => ({
        'Course Code': course.courseCode,
        'Course Name': course.courseName,
        'Department Code': course.department.departmentCode,
        'Instructor Email': course.instructor.email,
        'Credits': course.credits || 3,
        'Sessions Per Week': course.sessionsPerWeek || 2,
        'Session Duration': course.sessionDuration || 90,
        'Min Room Capacity': course.minRoomCapacity || 20,
        'Required Room Type': course.requiredRoomType || '',
        'Priority': course.priority || 'MEDIUM'
      }));

      // Add sample row if no data
      if (data.length === 0) {
        data.push({
          'Course Code': 'CS101',
          'Course Name': 'Introduction to Computer Science',
          'Department Code': 'CS',
          'Instructor Email': '<EMAIL>',
          'Credits': 3,
          'Sessions Per Week': 2,
          'Session Duration': 90,
          'Min Room Capacity': 30,
          'Required Room Type': 'Lecture Hall',
          'Priority': 'HIGH'
        });
      }

      const workbook = XLSX.utils.book_new();
      const worksheet = XLSX.utils.json_to_sheet(data);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Courses');

      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

      return {
        filename: `Courses_Template_${semester?.semesterName || 'Unknown'}_${new Date().toISOString().split('T')[0]}.xlsx`,
        buffer: Buffer.from(buffer),
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      };

    } catch (error) {
      logger.error('Error exporting courses template:', error);
      throw new Error(`Failed to export courses template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate schedule summary for export
   */
  private async generateScheduleSummary(semesterID: string) {
    try {
      // Get basic statistics
      const [totalLessons, totalCourses, totalInstructors, totalRooms] = await Promise.all([
        this.prisma.lesson.count({
          where: { course: { semesterID } }
        }),
        this.prisma.course.count({
          where: { semesterID }
        }),
        this.prisma.instructor.count({
          where: { courses: { some: { semesterID } } }
        }),
        this.prisma.room.count({
          where: { lessons: { some: { course: { semesterID } } } }
        })
      ]);

      // Get instructor workload
      const instructorWorkload = await this.prisma.lesson.groupBy({
        by: ['instructorID'],
        where: { course: { semesterID } },
        _sum: { duration: true },
        _count: { lessonID: true }
      });

      // Get room utilization
      const roomUtilization = await this.prisma.lesson.groupBy({
        by: ['roomID'],
        where: { course: { semesterID } },
        _count: { lessonID: true }
      });

      // Get department distribution
      const departmentDistribution = await this.prisma.lesson.groupBy({
        by: ['course'],
        where: { course: { semesterID } },
        _count: { lessonID: true }
      });

      const summaryData = [
        { 'Metric': 'Total Lessons', 'Value': totalLessons },
        { 'Metric': 'Total Courses', 'Value': totalCourses },
        { 'Metric': 'Active Instructors', 'Value': totalInstructors },
        { 'Metric': 'Rooms Used', 'Value': totalRooms },
        { 'Metric': 'Average Lessons per Instructor', 'Value': totalInstructors > 0 ? Math.round(totalLessons / totalInstructors * 100) / 100 : 0 },
        { 'Metric': 'Average Lessons per Room', 'Value': totalRooms > 0 ? Math.round(totalLessons / totalRooms * 100) / 100 : 0 }
      ];

      return summaryData;

    } catch (error) {
      logger.error('Error generating schedule summary:', error);
      return [
        { 'Metric': 'Error', 'Value': 'Failed to generate summary' }
      ];
    }
  }

  /**
   * Generate Excel template for bulk import
   */
  async generateImportTemplate(type: 'departments' | 'rooms' | 'instructors' | 'courses', semesterID?: string): Promise<ExcelExportData> {
    switch (type) {
      case 'departments':
        return this.exportDepartmentsTemplate();
      case 'rooms':
        return this.exportRoomsTemplate();
      case 'instructors':
        return this.exportInstructorsTemplate();
      case 'courses':
        if (!semesterID) {
          throw new Error('Semester ID is required for courses template');
        }
        return this.exportCoursesTemplate(semesterID);
      default:
        throw new Error('Invalid template type');
    }
  }

  /**
   * Validate Excel file structure before import
   */
  async validateExcelStructure(buffer: Buffer, type: 'departments' | 'rooms' | 'instructors' | 'courses'): Promise<{ valid: boolean; errors: string[] }> {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const data = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as any[][];

      if (data.length === 0) {
        return { valid: false, errors: ['Excel file is empty'] };
      }

      const headers = data[0] as string[];
      const errors: string[] = [];

      // Define required headers for each type
      const requiredHeaders: Record<string, string[]> = {
        departments: ['Department Name', 'Department Code'],
        rooms: ['Room Name', 'Capacity'],
        instructors: ['First Name', 'Last Name', 'Email', 'Department Code'],
        courses: ['Course Code', 'Course Name', 'Department Code', 'Instructor Email']
      };

      const required = requiredHeaders[type];
      for (const header of required) {
        if (!headers.includes(header)) {
          errors.push(`Missing required column: ${header}`);
        }
      }

      return {
        valid: errors.length === 0,
        errors
      };

    } catch (error) {
      return {
        valid: false,
        errors: [`Failed to validate Excel structure: ${error instanceof Error ? error.message : 'Unknown error'}`]
      };
    }
  }
}