{"name": "university-schedule-assistant", "version": "1.0.0", "description": "A comprehensive university scheduling system with intelligent automation", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["university", "scheduling", "timetable", "education", "automation", "constraint-satisfaction"], "author": "University Schedule Assistant Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "exceljs": "^4.4.0", "puppeteer": "^21.6.1", "socket.io": "^4.7.4", "bull": "^4.12.2", "redis": "^4.6.12", "winston": "^3.11.0", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node": "^10.9.2", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prettier": "^3.1.1", "prisma": "^5.7.1"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}