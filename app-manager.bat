@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Application Manager
:: This script combines all application management functions

echo ========================================
echo University Schedule Assistant Manager
echo ========================================
echo.

:: Set color for better visibility
color 0B

:: Configuration
set "BACKEND_PORT=3001"
set "FRONTEND_PORT=3000"
set "DB_PORT=5432"
set "REDIS_PORT=6379"

echo [INFO] University Schedule Assistant Management Console
echo.

echo Available Actions:
echo 1. Start Application (Full Setup)
echo 2. Stop Application
echo 3. Check Status
echo 4. Restart Application
echo 5. Open Browser (Fresh)
echo 6. View Logs
echo 7. Database Verification
echo 8. Backup Data
echo 9. Exit
echo.

set /p "choice=Choose action (1-9): "

if "!choice!"=="1" goto start_app
if "!choice!"=="2" goto stop_app
if "!choice!"=="3" goto check_status
if "!choice!"=="4" goto restart_app
if "!choice!"=="5" goto open_browser
if "!choice!"=="6" goto view_logs
if "!choice!"=="7" goto verify_database
if "!choice!"=="8" goto backup_data
if "!choice!"=="9" goto exit_app

echo [ERROR] Invalid choice
pause
goto :eof

:start_app
echo.
echo ========================================
echo Starting University Schedule Assistant
echo ========================================
echo.

:: Check prerequisites
call :check_prerequisites
if %errorLevel% neq 0 goto :eof

:: Setup environment
call :setup_environment
if %errorLevel% neq 0 goto :eof

:: Start services
call :start_services
if %errorLevel% neq 0 goto :eof

:: Verify database
call :verify_database_internal

:: Final health checks
call :health_checks

:: Offer browser opening
echo [INFO] Would you like to open the application in your browser? (Y/N)
set /p "open_browser="
if /i "!open_browser!"=="Y" (
    call :open_browser_internal
)

echo.
echo [SUCCESS] Application started successfully!
pause
goto :eof

:stop_app
echo.
echo ========================================
echo Stopping University Schedule Assistant
echo ========================================
echo.

echo [INFO] Stopping all services...
docker-compose down
if %errorLevel% neq 0 (
    echo [ERROR] Failed to stop some services
) else (
    echo [SUCCESS] All services stopped successfully
)

echo [INFO] Would you like to clean up unused Docker resources? (Y/N)
set /p "cleanup="
if /i "!cleanup!"=="Y" (
    echo [INFO] Cleaning up unused Docker resources...
    docker system prune -f
    echo [SUCCESS] Cleanup completed
)

echo.
echo [SUCCESS] Application stopped!
pause
goto :eof

:check_status
echo.
echo ========================================
echo University Schedule Assistant Status
echo ========================================
echo.

:: Docker status
echo [INFO] Docker Status:
docker info >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Docker is running
) else (
    echo [ERROR] Docker is not running
)
echo.

:: Container status
echo [INFO] Container Status:
docker-compose ps
echo.

:: Service health checks
echo [INFO] Service Health Checks:
call :health_checks

:: Resource usage
echo [INFO] Resource Usage:
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
echo.

:: Application URLs
echo [INFO] Application URLs:
echo Frontend:     http://localhost:%FRONTEND_PORT%
echo Backend API:  http://localhost:%BACKEND_PORT%
echo Health Check: http://localhost:%BACKEND_PORT%/health
echo.

pause
goto :eof

:restart_app
echo.
echo ========================================
echo Restarting University Schedule Assistant
echo ========================================
echo.

echo [INFO] Stopping services...
docker-compose down
timeout /t 5 /nobreak >nul

echo [INFO] Starting services...
call :start_services

echo [SUCCESS] Application restarted!
pause
goto :eof

:open_browser
echo.
echo ========================================
echo Opening Fresh Browser Instance
echo ========================================
echo.

call :open_browser_internal
pause
goto :eof

:view_logs
echo.
echo ========================================
echo Application Logs
echo ========================================
echo.

echo Available log options:
echo 1. All services (live)
echo 2. Backend API only
echo 3. Frontend only
echo 4. Database only
echo 5. Redis only
echo.

set /p "log_choice=Choose log option (1-5): "

if "!log_choice!"=="1" (
    echo [INFO] Showing live logs for all services (Press Ctrl+C to exit)...
    docker-compose logs -f
) else if "!log_choice!"=="2" (
    echo [INFO] Backend API logs:
    docker-compose logs --tail=50 api
) else if "!log_choice!"=="3" (
    echo [INFO] Frontend logs:
    docker-compose logs --tail=50 frontend
) else if "!log_choice!"=="4" (
    echo [INFO] Database logs:
    docker-compose logs --tail=50 postgres
) else if "!log_choice!"=="5" (
    echo [INFO] Redis logs:
    docker-compose logs --tail=50 redis
) else (
    echo [ERROR] Invalid choice
)

pause
goto :eof

:verify_database
echo.
echo ========================================
echo Database Verification
echo ========================================
echo.

call :verify_database_internal
pause
goto :eof

:backup_data
echo.
echo ========================================
echo Data Backup
echo ========================================
echo.

call :backup_data_internal
pause
goto :eof

:exit_app
echo.
echo [INFO] Goodbye!
exit /b 0

:: ========================================
:: FUNCTION IMPLEMENTATIONS
:: ========================================

:check_prerequisites
echo [INFO] Checking prerequisites...

:: Check Docker
where docker >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker is not installed or not in PATH
    echo [INFO] Please install Docker Desktop from https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

where docker-compose >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker Compose is not installed or not in PATH
    pause
    exit /b 1
)

:: Check Node.js
where node >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [INFO] Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check Docker service
docker info >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker service is not running
    echo [INFO] Starting Docker Desktop...
    start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    echo [INFO] Waiting for Docker to start...

    set /a "attempts=0"
    :wait_docker
    set /a "attempts+=1"
    if %attempts% gtr 30 (
        echo [ERROR] Docker failed to start
        pause
        exit /b 1
    )
    timeout /t 5 /nobreak >nul
    docker info >nul 2>&1
    if %errorLevel% neq 0 goto wait_docker
)

echo [SUCCESS] All prerequisites are available
exit /b 0

:setup_environment
echo [INFO] Setting up environment...

:: Create necessary directories
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups
if not exist "nginx\ssl" mkdir nginx\ssl

:: Check .env file
if not exist ".env" (
    echo [INFO] Creating .env file from template...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo [SUCCESS] .env file created from template
        echo [WARNING] Please edit .env file with your configuration
        notepad .env
        echo [INFO] Press any key after editing .env file...
        pause >nul
    ) else (
        echo [ERROR] No .env.example template found
        echo [INFO] Creating basic .env file...
        call :create_basic_env
        notepad .env
        echo [INFO] Press any key after editing .env file...
        pause >nul
    )
)

echo [SUCCESS] Environment setup completed
exit /b 0

:create_basic_env
(
    echo # University Schedule Assistant - Environment Configuration
    echo NODE_ENV=development
    echo PORT=3001
    echo.
    echo # Database Configuration
    echo DATABASE_URL="**********************************************************/schedule_assistant?schema=public"
    echo.
    echo # JWT Configuration
    echo JWT_SECRET=your_very_secure_jwt_secret_key_here_minimum_32_characters_long
    echo JWT_REFRESH_SECRET=your_very_secure_jwt_refresh_secret_key_here_minimum_32_characters_long
    echo.
    echo # Redis Configuration
    echo REDIS_URL=redis://redis:6379
    echo REDIS_PASSWORD=
    echo.
    echo # CORS Configuration
    echo ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
) > .env
exit /b 0

:start_services
echo [INFO] Starting services in stages...

:: Stop any existing containers
echo [INFO] Stopping any existing containers...
docker-compose down >nul 2>&1

:: Install dependencies
echo [INFO] Installing dependencies...
if exist "package.json" (
    npm install >nul 2>&1
    if %errorLevel% neq 0 (
        echo [WARNING] Backend dependency installation failed
    )
)

if exist "frontend\package.json" (
    cd frontend
    npm install >nul 2>&1
    if %errorLevel% neq 0 (
        echo [WARNING] Frontend dependency installation failed
    )
    cd ..
)

:: Start database and Redis first
echo [INFO] Starting database and Redis...
docker-compose up -d postgres redis
if %errorLevel% neq 0 (
    echo [ERROR] Failed to start database services
    pause
    exit /b 1
)

:: Wait for database and Redis
echo [INFO] Waiting for database and Redis to be ready...
timeout /t 15 /nobreak >nul

:: Start API service
echo [INFO] Starting API service...
docker-compose up -d --build api
if %errorLevel% neq 0 (
    echo [ERROR] Failed to start API service
    docker-compose logs api
    pause
    exit /b 1
)

:: Wait for API
echo [INFO] Waiting for API to initialize...
timeout /t 15 /nobreak >nul

:: Start frontend service
echo [INFO] Starting frontend service...
docker-compose up -d --build frontend
if %errorLevel% neq 0 (
    echo [ERROR] Failed to start frontend service
    docker-compose logs frontend
    pause
    exit /b 1
)

:: Wait for all services
echo [INFO] Waiting for all services to be ready...
timeout /t 10 /nobreak >nul

:: Run database migrations
echo [INFO] Running database migrations...
docker-compose exec -T api npx prisma migrate deploy >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Database migration failed or no migrations to run
) else (
    echo [SUCCESS] Database migrations completed
)

:: Generate Prisma client
echo [INFO] Generating Prisma client...
docker-compose exec -T api npx prisma generate >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Prisma client generation failed
) else (
    echo [SUCCESS] Prisma client generated
)

echo [SUCCESS] All services started
exit /b 0

:health_checks
:: Backend health check
curl -f http://localhost:%BACKEND_PORT%/health >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Backend is healthy (http://localhost:%BACKEND_PORT%)
) else (
    echo [ERROR] Backend is not responding (http://localhost:%BACKEND_PORT%)
)

:: Frontend health check
powershell -command "try { $response = Invoke-WebRequest -Uri 'http://localhost:%FRONTEND_PORT%' -TimeoutSec 5 -UseBasicParsing; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Frontend is healthy (http://localhost:%FRONTEND_PORT%)
) else (
    echo [ERROR] Frontend is not responding (http://localhost:%FRONTEND_PORT%)
)

:: Database connection check
docker-compose exec -T postgres pg_isready -U schedule_user -d schedule_assistant >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Database is healthy
) else (
    echo [ERROR] Database is not responding
)

:: Redis connection check
docker-compose exec -T redis redis-cli ping >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Redis is healthy
) else (
    echo [ERROR] Redis is not responding
)
exit /b 0

:open_browser_internal
echo [INFO] Checking if application is running...
powershell -command "try { $response = Invoke-WebRequest -Uri 'http://localhost:%FRONTEND_PORT%' -TimeoutSec 5 -UseBasicParsing; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Application is not running
    echo [INFO] Please start the application first
    exit /b 1
)

echo [INFO] Clearing browser cache...
powershell -command "Start-Process -FilePath 'rundll32.exe' -ArgumentList 'InetCpl.cpl,ClearMyTracksByProcess 255' -Wait" >nul 2>&1

echo [INFO] Opening fresh application instance...
timeout /t 2 /nobreak >nul
start "University Schedule Assistant" "http://localhost:%FRONTEND_PORT%?t=%random%&cache=false"
echo [SUCCESS] Application opened in browser
exit /b 0

:verify_database_internal
echo [INFO] Verifying database setup...

:: Check if database container is running
docker-compose ps postgres | findstr "Up" >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Database container is not running
    exit /b 1
)
echo [SUCCESS] Database container is running

:: Check database connection
docker-compose exec -T postgres pg_isready -U schedule_user -d schedule_assistant >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Database connection is healthy
) else (
    echo [ERROR] Database connection failed
    exit /b 1
)

:: Check if database exists and has tables
echo [INFO] Checking database structure...
docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -c "\dt" >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Database tables exist

    :: Show table count
    for /f "tokens=*" %%i in ('docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -t -c "SELECT count(*) FROM information_schema.tables WHERE table_schema = 'public';"') do (
        set "table_count=%%i"
    )
    echo [INFO] Database has !table_count! tables

    :: List tables
    echo [INFO] Database tables:
    docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -c "\dt"
) else (
    echo [WARNING] No tables found in database
    echo [INFO] This might be normal for a fresh installation
)

:: Check Prisma migrations
echo [INFO] Checking Prisma migration status...
docker-compose exec -T api npx prisma migrate status >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Prisma migrations are up to date
) else (
    echo [WARNING] Prisma migrations may need to be applied
    echo [INFO] Run: docker-compose exec api npx prisma migrate deploy
)

:: Check for sample data
echo [INFO] Checking for sample data...
docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -t -c "SELECT COUNT(*) FROM \"User\";" 2>nul | findstr /r "[0-9]" >nul
if %errorLevel% equ 0 (
    for /f "tokens=*" %%i in ('docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -t -c "SELECT COUNT(*) FROM \"User\";"') do (
        set "user_count=%%i"
    )
    echo [INFO] Database has !user_count! users
) else (
    echo [INFO] No user data found (normal for fresh installation)
)

echo [SUCCESS] Database verification completed
exit /b 0

:backup_data_internal
set "BACKUP_DIR=backups"
set "TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"
set "BACKUP_PATH=%BACKUP_DIR%\backup_%TIMESTAMP%"

echo [INFO] Creating backup at: %BACKUP_PATH%

:: Create backup directory
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
if not exist "%BACKUP_PATH%" mkdir "%BACKUP_PATH%"

:: Check if application is running
docker-compose ps | findstr "Up" >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Application is not running - some backups may fail
)

:: Backup database
echo [INFO] Backing up database...
docker-compose exec -T postgres pg_dump -U schedule_user schedule_assistant > "%BACKUP_PATH%\database_backup.sql" 2>nul
if %errorLevel% equ 0 (
    echo [SUCCESS] Database backup completed
) else (
    echo [ERROR] Database backup failed
)

:: Backup uploads
echo [INFO] Backing up uploads...
if exist "uploads" (
    xcopy "uploads" "%BACKUP_PATH%\uploads" /E /I /Q >nul 2>&1
    echo [SUCCESS] Uploads backup completed
) else (
    echo [INFO] No uploads directory found
)

:: Backup logs
echo [INFO] Backing up logs...
if exist "logs" (
    xcopy "logs" "%BACKUP_PATH%\logs" /E /I /Q >nul 2>&1
    echo [SUCCESS] Logs backup completed
) else (
    echo [INFO] No logs directory found
)

:: Backup configuration
echo [INFO] Backing up configuration files...
if exist ".env" copy ".env" "%BACKUP_PATH%\.env" >nul 2>&1
if exist "docker-compose.yml" copy "docker-compose.yml" "%BACKUP_PATH%\docker-compose.yml" >nul 2>&1
if exist "docker-compose.prod.yml" copy "docker-compose.prod.yml" "%BACKUP_PATH%\docker-compose.prod.yml" >nul 2>&1

:: Create backup info
(
    echo Backup Information
    echo ==================
    echo Backup Date: %date% %time%
    echo Backup Location: %BACKUP_PATH%
    echo.
    echo Contents:
    echo - Database dump: database_backup.sql
    echo - Uploads directory: uploads/
    echo - Logs directory: logs/
    echo - Configuration files: .env, docker-compose files
) > "%BACKUP_PATH%\backup_info.txt"

echo [SUCCESS] Backup completed at: %BACKUP_PATH%

:: Cleanup old backups (keep last 10)
set /a "count=0"
for /f "skip=10 delims=" %%i in ('dir "%BACKUP_DIR%\backup_*" /b /o-d 2^>nul') do (
    set /a "count+=1"
    rmdir /s /q "%BACKUP_DIR%\%%i" 2>nul
)
if %count% gtr 0 (
    echo [INFO] Removed %count% old backup(s)
)

exit /b 0