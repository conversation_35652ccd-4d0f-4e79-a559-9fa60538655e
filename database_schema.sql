-- University Schedule Assistant Database Schema
-- Comprehensive schema for academic scheduling system

-- =============================================
-- CORE ENTITIES
-- =============================================

-- Semester table
CREATE TABLE semesters (
    semester_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Department table
CREATE TABLE departments (
    department_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL UNIQUE,
    code VARCHAR(10) UNIQUE,
    head_instructor_id UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Room types enum
CREATE TYPE room_type_enum AS ENUM (
    'Lecture Hall',
    'Classroom',
    'Lab',
    'Tutorial Room',
    'Computer Lab'
);

-- Rooms table
CREATE TABLE rooms (
    room_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    room_number VARCHAR(50) NOT NULL UNIQUE,
    max_capacity INTEGER NOT NULL CHECK (max_capacity > 0),
    type room_type_enum NOT NULL,
    assigned_department_id UUID REFERENCES departments(department_id),
    building VARCHAR(100),
    floor_number INTEGER,
    equipment_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Instructor availability structure (JSON)
-- Format: {"Monday": [1,2,3,4,5], "Tuesday": [], "Wednesday": [1,2,3,4,5,6,7,8], ...}
-- Numbers represent time slots (1-26)

-- Instructors table
CREATE TABLE instructors (
    instructor_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    email VARCHAR(255) UNIQUE,
    department_id UUID NOT NULL REFERENCES departments(department_id),
    availability JSONB DEFAULT '{"Monday":[],"Tuesday":[],"Wednesday":[],"Thursday":[],"Friday":[],"Saturday":[],"Sunday":[]}',
    max_daily_slots INTEGER DEFAULT 20,
    preferred_times JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Student Level table (e.g., "First Year Pharmacy")
CREATE TABLE student_levels (
    level_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(department_id),
    expected_student_count INTEGER NOT NULL CHECK (expected_student_count > 0),
    year_number INTEGER,
    semester_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Group types enum
CREATE TYPE group_type_enum AS ENUM (
    'Full Level',
    'Group',
    'Sub-Group'
);

-- Student Groups table
CREATE TABLE student_groups (
    group_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level_id UUID NOT NULL REFERENCES student_levels(level_id),
    group_name VARCHAR(100) NOT NULL,
    parent_group_id UUID REFERENCES student_groups(group_id),
    group_type group_type_enum NOT NULL,
    student_count INTEGER NOT NULL CHECK (student_count > 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(level_id, group_name)
);

-- Courses table (abstract definition)
CREATE TABLE courses (
    course_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_code VARCHAR(20) NOT NULL,
    title VARCHAR(300) NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(department_id),
    credits INTEGER,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(course_code, department_id)
);

-- Lesson types enum
CREATE TYPE lesson_type_enum AS ENUM (
    'Lecture',
    'Lab',
    'Tutorial',
    'Computer Lab',
    'Seminar',
    'Workshop'
);

-- Lessons table (concrete instances to be scheduled)
CREATE TABLE lessons (
    lesson_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    student_group_id UUID NOT NULL REFERENCES student_groups(group_id),
    instructor_id UUID NOT NULL REFERENCES instructors(instructor_id),
    duration_in_slots INTEGER NOT NULL CHECK (duration_in_slots > 0),
    lesson_type lesson_type_enum NOT NULL,
    required_room_type room_type_enum NOT NULL,
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    sessions_per_week INTEGER DEFAULT 1,
    total_sessions INTEGER,
    priority INTEGER DEFAULT 5, -- 1-10 scale for scheduling priority
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- SCHEDULING TABLES
-- =============================================

-- Days of week enum
CREATE TYPE day_of_week_enum AS ENUM (
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
);

-- Schedule entries (the actual timetable)
CREATE TABLE schedule_entries (
    entry_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lesson_id UUID NOT NULL REFERENCES lessons(lesson_id),
    room_id UUID NOT NULL REFERENCES rooms(room_id),
    day_of_week day_of_week_enum NOT NULL,
    start_slot INTEGER NOT NULL CHECK (start_slot >= 1 AND start_slot <= 26),
    end_slot INTEGER NOT NULL CHECK (end_slot >= 1 AND end_slot <= 26),
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    week_number INTEGER, -- For recurring schedules
    is_locked BOOLEAN DEFAULT FALSE, -- Prevents auto-rescheduling
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CHECK (end_slot > start_slot)
);

-- =============================================
-- CONSTRAINT MANAGEMENT
-- =============================================

-- Constraint types enum
CREATE TYPE constraint_type_enum AS ENUM (
    'HARD',
    'SOFT'
);

-- Constraint categories enum
CREATE TYPE constraint_category_enum AS ENUM (
    'CONTIGUITY',
    'TIME_PLACEMENT',
    'RESOURCE_USAGE',
    'COMBINATION'
);

-- Scheduling constraints table
CREATE TABLE scheduling_constraints (
    constraint_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    constraint_type constraint_type_enum NOT NULL,
    constraint_category constraint_category_enum NOT NULL,
    rule_definition JSONB NOT NULL, -- Flexible JSON structure for different rule types
    weight INTEGER DEFAULT 1, -- For soft constraints
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- SUBSTITUTION MANAGEMENT
-- =============================================

-- Substitution status enum
CREATE TYPE substitution_status_enum AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED',
    'COMPLETED'
);

-- Daily substitutions table
CREATE TABLE substitutions (
    substitution_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    original_entry_id UUID NOT NULL REFERENCES schedule_entries(entry_id),
    absent_instructor_id UUID NOT NULL REFERENCES instructors(instructor_id),
    substitute_instructor_id UUID REFERENCES instructors(instructor_id),
    substitution_date DATE NOT NULL,
    reason TEXT,
    status substitution_status_enum DEFAULT 'PENDING',
    notes TEXT,
    created_by UUID, -- User who created the substitution
    approved_by UUID, -- User who approved the substitution
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- WORKFLOW MANAGEMENT
-- =============================================

-- Generation status enum
CREATE TYPE generation_status_enum AS ENUM (
    'NOT_STARTED',
    'DATA_INPUT',
    'MANIFEST_GENERATED',
    'MANIFEST_VALIDATED',
    'SCHEDULING_IN_PROGRESS',
    'SCHEDULING_COMPLETED',
    'SCHEDULING_FAILED',
    'PUBLISHED'
);

-- Schedule generation sessions
CREATE TABLE generation_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    status generation_status_enum DEFAULT 'NOT_STARTED',
    manifest_file_path VARCHAR(500),
    generation_started_at TIMESTAMP,
    generation_completed_at TIMESTAMP,
    total_lessons INTEGER,
    scheduled_lessons INTEGER,
    unscheduled_lessons INTEGER,
    constraint_violations JSONB,
    error_log TEXT,
    created_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Core entity indexes
CREATE INDEX idx_instructors_department ON instructors(department_id);
CREATE INDEX idx_instructors_active ON instructors(is_active);
CREATE INDEX idx_rooms_type ON rooms(type);
CREATE INDEX idx_rooms_department ON rooms(assigned_department_id);
CREATE INDEX idx_student_groups_level ON student_groups(level_id);
CREATE INDEX idx_student_groups_parent ON student_groups(parent_group_id);
CREATE INDEX idx_lessons_course ON lessons(course_id);
CREATE INDEX idx_lessons_group ON lessons(student_group_id);
CREATE INDEX idx_lessons_instructor ON lessons(instructor_id);
CREATE INDEX idx_lessons_semester ON lessons(semester_id);

-- Scheduling indexes
CREATE INDEX idx_schedule_entries_lesson ON schedule_entries(lesson_id);
CREATE INDEX idx_schedule_entries_room ON schedule_entries(room_id);
CREATE INDEX idx_schedule_entries_day_slot ON schedule_entries(day_of_week, start_slot, end_slot);
CREATE INDEX idx_schedule_entries_semester ON schedule_entries(semester_id);

-- Substitution indexes
CREATE INDEX idx_substitutions_date ON substitutions(substitution_date);
CREATE INDEX idx_substitutions_original_instructor ON substitutions(absent_instructor_id);
CREATE INDEX idx_substitutions_substitute ON substitutions(substitute_instructor_id);
CREATE INDEX idx_substitutions_status ON substitutions(status);

-- =============================================
-- FOREIGN KEY CONSTRAINTS
-- =============================================

ALTER TABLE departments ADD CONSTRAINT fk_departments_head
    FOREIGN KEY (head_instructor_id) REFERENCES instructors(instructor_id);

-- =============================================
-- TIME SLOT REFERENCE (FOR DOCUMENTATION)
-- =============================================

-- Time slots mapping (1-26):
-- Slot 1: 09:30-09:45, Slot 2: 09:45-10:00, ..., Slot 26: 15:45-16:00
-- This mapping is implemented in application logic, not stored in database

/*
Time Slot Reference Table:
Slot | Start Time | End Time
-----|------------|----------
1    | 09:30      | 09:45
2    | 09:45      | 10:00
3    | 10:00      | 10:15
4    | 10:15      | 10:30
5    | 10:30      | 10:45
6    | 10:45      | 11:00
7    | 11:00      | 11:15
8    | 11:15      | 11:30
9    | 11:30      | 11:45
10   | 11:45      | 12:00
11   | 12:00      | 12:15
12   | 12:15      | 12:30
13   | 12:30      | 12:45
14   | 12:45      | 13:00
15   | 13:00      | 13:15
16   | 13:15      | 13:30
17   | 13:30      | 13:45
18   | 13:45      | 14:00
19   | 14:00      | 14:15
20   | 14:15      | 14:30
21   | 14:30      | 14:45
22   | 14:45      | 15:00
23   | 15:00      | 15:15
24   | 15:15      | 15:30
25   | 15:30      | 15:45
26   | 15:45      | 16:00
*/