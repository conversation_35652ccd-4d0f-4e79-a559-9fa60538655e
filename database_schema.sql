-- University Schedule Assistant Database Schema - Phase 1 Implementation
-- Refined schema based on implementation requirements

-- =============================================
-- CORE ENTITIES (Phase 1 Implementation)
-- =============================================

-- Semesters table
CREATE TABLE Semesters (
    semesterID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    startDate DATE NOT NULL,
    endDate DATE NOT NULL,
    isActive BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Departments table
CREATE TABLE Departments (
    departmentID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL UNIQUE,
    code VARCHAR(10) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Room types enum
CREATE TYPE room_type_enum AS ENUM (
    'Lecture Hall',
    'Classroom',
    'Lab',
    'Tutorial Room',
    'Computer Lab'
);

-- Rooms table
CREATE TABLE Rooms (
    roomID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    roomNumber VARCHAR(50) NOT NULL UNIQUE,
    maxCapacity INTEGER NOT NULL CHECK (maxCapacity > 0),
    type room_type_enum NOT NULL,
    assignedDepartmentID UUID REFERENCES Departments(departmentID),
    building VARCHAR(100),
    floor_number INTEGER,
    equipment_notes TEXT,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Instructors table
CREATE TABLE Instructors (
    instructorID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    email VARCHAR(255) UNIQUE,
    departmentID UUID NOT NULL REFERENCES Departments(departmentID),
    maxDailySlots INTEGER DEFAULT 20,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Normalized Instructor Availability table
CREATE TABLE InstructorAvailability (
    availabilityID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    instructorID UUID NOT NULL REFERENCES Instructors(instructorID) ON DELETE CASCADE,
    dayOfWeek VARCHAR(10) NOT NULL CHECK (dayOfWeek IN ('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')),
    availableSlots JSONB NOT NULL DEFAULT '[]', -- Array of slot numbers [1,2,3,4,5,...]
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(instructorID, dayOfWeek)
);

-- Student Levels table
CREATE TABLE StudentLevels (
    levelID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    departmentID UUID NOT NULL REFERENCES Departments(departmentID),
    expectedStudentCount INTEGER NOT NULL CHECK (expectedStudentCount > 0),
    yearNumber INTEGER,
    semesterNumber INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Group types enum
CREATE TYPE group_type_enum AS ENUM (
    'Full Level',
    'Group',
    'Sub-Group'
);

-- Student Groups table
CREATE TABLE StudentGroups (
    groupID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    levelID UUID NOT NULL REFERENCES StudentLevels(levelID),
    groupName VARCHAR(100) NOT NULL,
    parentGroupID UUID REFERENCES StudentGroups(groupID),
    groupType group_type_enum NOT NULL,
    studentCount INTEGER NOT NULL CHECK (studentCount > 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(levelID, groupName)
);

-- Courses table
CREATE TABLE Courses (
    courseID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    courseCode VARCHAR(20) NOT NULL,
    title VARCHAR(300) NOT NULL,
    departmentID UUID NOT NULL REFERENCES Departments(departmentID),
    credits INTEGER,
    description TEXT,
    isActive BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(courseCode, departmentID)
);

-- Lesson types enum
CREATE TYPE lesson_type_enum AS ENUM (
    'Lecture',
    'Lab',
    'Tutorial',
    'Computer Lab',
    'Seminar',
    'Workshop'
);

-- Lessons table (The "problem definition" - instances to be scheduled)
CREATE TABLE Lessons (
    lessonID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    courseID UUID NOT NULL REFERENCES Courses(courseID),
    studentGroupID UUID NOT NULL REFERENCES StudentGroups(groupID),
    instructorID UUID NOT NULL REFERENCES Instructors(instructorID),
    durationInSlots INTEGER NOT NULL CHECK (durationInSlots > 0),
    lessonType lesson_type_enum NOT NULL,
    requiredRoomType room_type_enum NOT NULL,
    semesterID UUID NOT NULL REFERENCES Semesters(semesterID),
    sessionsPerWeek INTEGER DEFAULT 1,
    totalSessions INTEGER,
    priority INTEGER DEFAULT 5 CHECK (priority >= 1 AND priority <= 10),
    specialRequirements TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- OUTPUT & OPERATIONAL TABLES
-- =============================================

-- Schedule status enum
CREATE TYPE schedule_status_enum AS ENUM (
    'Scheduled',
    'Locked',
    'Cancelled',
    'Modified'
);

-- Days of week enum
CREATE TYPE day_of_week_enum AS ENUM (
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
);

-- Scheduled Lessons table (Final generated schedule)
CREATE TABLE ScheduledLessons (
    scheduledLessonID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lessonID UUID NOT NULL REFERENCES Lessons(lessonID),
    roomID UUID NOT NULL REFERENCES Rooms(roomID),
    dayOfWeek day_of_week_enum NOT NULL,
    startSlot INTEGER NOT NULL CHECK (startSlot >= 1 AND startSlot <= 26),
    endSlot INTEGER NOT NULL CHECK (endSlot >= 1 AND endSlot <= 26),
    semesterID UUID NOT NULL REFERENCES Semesters(semesterID),
    weekNumber INTEGER DEFAULT 1,
    status schedule_status_enum DEFAULT 'Scheduled',
    lockedBy UUID, -- User who locked this lesson
    lockedAt TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CHECK (endSlot > startSlot),
    UNIQUE(roomID, dayOfWeek, startSlot, endSlot, semesterID, weekNumber),
    UNIQUE(lessonID, dayOfWeek, startSlot, endSlot, semesterID, weekNumber)
);

-- Substitution status enum
CREATE TYPE substitution_status_enum AS ENUM (
    'Pending',
    'Approved',
    'Rejected',
    'Completed'
);

-- Substitutions table
CREATE TABLE Substitutions (
    substitutionID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    originalInstructorID UUID NOT NULL REFERENCES Instructors(instructorID),
    replacementInstructorID UUID REFERENCES Instructors(instructorID),
    scheduledLessonID UUID NOT NULL REFERENCES ScheduledLessons(scheduledLessonID),
    dateOfAbsence DATE NOT NULL,
    reason TEXT,
    status substitution_status_enum DEFAULT 'Pending',
    notes TEXT,
    requestedBy UUID, -- User who requested the substitution
    approvedBy UUID, -- User who approved the substitution
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Published Schedules table (Stable snapshot for public view)
CREATE TABLE PublishedSchedules (
    publishedScheduleID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    semesterID UUID NOT NULL REFERENCES Semesters(semesterID),
    scheduleData JSONB NOT NULL, -- Complete schedule snapshot
    publishedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    publishedBy UUID, -- User who published the schedule
    version INTEGER DEFAULT 1,
    isActive BOOLEAN DEFAULT TRUE,
    notes TEXT
);

-- =============================================
-- CONSTRAINT & RULE STORAGE
-- =============================================

-- Constraint types enum
CREATE TYPE constraint_type_enum AS ENUM (
    'Hard',
    'Soft',
    'Advanced'
);

-- Scheduling Constraints table (Flexible rule storage)
CREATE TABLE SchedulingConstraints (
    constraintID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    constraintType constraint_type_enum NOT NULL,
    ruleDefinition JSONB NOT NULL, -- Flexible JSON structure for different rule types
    weight INTEGER DEFAULT 1 CHECK (weight >= 1 AND weight <= 10), -- For soft constraints
    isActive BOOLEAN DEFAULT TRUE,
    semesterID UUID REFERENCES Semesters(semesterID), -- Semester-specific constraints
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- WORKFLOW MANAGEMENT TABLES
-- =============================================

-- Generation status enum
CREATE TYPE generation_status_enum AS ENUM (
    'NOT_STARTED',
    'DATA_INPUT',
    'MANIFEST_GENERATED',
    'MANIFEST_VALIDATED',
    'SCHEDULING_IN_PROGRESS',
    'SCHEDULING_COMPLETED',
    'SCHEDULING_FAILED',
    'PUBLISHED'
);

-- Schedule Generation Sessions (For workflow tracking)
CREATE TABLE GenerationSessions (
    sessionID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    semesterID UUID NOT NULL REFERENCES Semesters(semesterID),
    status generation_status_enum DEFAULT 'NOT_STARTED',
    manifestFilePath VARCHAR(500),
    jobID VARCHAR(100), -- Celery job ID for async tracking
    generationStartedAt TIMESTAMP,
    generationCompletedAt TIMESTAMP,
    totalLessons INTEGER,
    scheduledLessons INTEGER,
    unscheduledLessons INTEGER,
    qualityScore DECIMAL(5,2),
    constraintViolations JSONB,
    errorLog TEXT,
    createdBy UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- USER MANAGEMENT (Basic structure)
-- =============================================

-- User roles enum
CREATE TYPE user_role_enum AS ENUM (
    'admin',
    'scheduler',
    'department_coordinator',
    'instructor',
    'student'
);

-- Users table (Basic user management)
CREATE TABLE Users (
    userID UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(100) NOT NULL UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    passwordHash VARCHAR(255) NOT NULL,
    role user_role_enum NOT NULL DEFAULT 'instructor',
    departmentID UUID REFERENCES Departments(departmentID),
    instructorID UUID REFERENCES Instructors(instructorID), -- Link to instructor if applicable
    isActive BOOLEAN DEFAULT TRUE,
    lastLogin TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- PERFORMANCE INDEXES
-- =============================================

-- Core entity indexes
CREATE INDEX idx_instructors_department ON Instructors(departmentID);
CREATE INDEX idx_instructors_active ON Instructors(isActive);
CREATE INDEX idx_instructor_availability_instructor ON InstructorAvailability(instructorID);
CREATE INDEX idx_instructor_availability_day ON InstructorAvailability(dayOfWeek);

CREATE INDEX idx_rooms_type ON Rooms(type);
CREATE INDEX idx_rooms_department ON Rooms(assignedDepartmentID);
CREATE INDEX idx_rooms_active ON Rooms(isActive);

CREATE INDEX idx_student_groups_level ON StudentGroups(levelID);
CREATE INDEX idx_student_groups_parent ON StudentGroups(parentGroupID);

CREATE INDEX idx_courses_department ON Courses(departmentID);
CREATE INDEX idx_courses_active ON Courses(isActive);
CREATE INDEX idx_courses_code ON Courses(courseCode);

CREATE INDEX idx_lessons_course ON Lessons(courseID);
CREATE INDEX idx_lessons_group ON Lessons(studentGroupID);
CREATE INDEX idx_lessons_instructor ON Lessons(instructorID);
CREATE INDEX idx_lessons_semester ON Lessons(semesterID);
CREATE INDEX idx_lessons_type ON Lessons(lessonType);

-- Scheduling indexes
CREATE INDEX idx_scheduled_lessons_lesson ON ScheduledLessons(lessonID);
CREATE INDEX idx_scheduled_lessons_room ON ScheduledLessons(roomID);
CREATE INDEX idx_scheduled_lessons_day_slot ON ScheduledLessons(dayOfWeek, startSlot, endSlot);
CREATE INDEX idx_scheduled_lessons_semester ON ScheduledLessons(semesterID);
CREATE INDEX idx_scheduled_lessons_status ON ScheduledLessons(status);

-- Substitution indexes
CREATE INDEX idx_substitutions_date ON Substitutions(dateOfAbsence);
CREATE INDEX idx_substitutions_original_instructor ON Substitutions(originalInstructorID);
CREATE INDEX idx_substitutions_replacement_instructor ON Substitutions(replacementInstructorID);
CREATE INDEX idx_substitutions_status ON Substitutions(status);
CREATE INDEX idx_substitutions_scheduled_lesson ON Substitutions(scheduledLessonID);

-- Constraint indexes
CREATE INDEX idx_constraints_type ON SchedulingConstraints(constraintType);
CREATE INDEX idx_constraints_active ON SchedulingConstraints(isActive);
CREATE INDEX idx_constraints_semester ON SchedulingConstraints(semesterID);

-- Generation session indexes
CREATE INDEX idx_generation_sessions_semester ON GenerationSessions(semesterID);
CREATE INDEX idx_generation_sessions_status ON GenerationSessions(status);
CREATE INDEX idx_generation_sessions_job ON GenerationSessions(jobID);

-- User indexes
CREATE INDEX idx_users_role ON Users(role);
CREATE INDEX idx_users_department ON Users(departmentID);
CREATE INDEX idx_users_instructor ON Users(instructorID);
CREATE INDEX idx_users_active ON Users(isActive);

-- =============================================
-- SAMPLE DATA INSERTS (For Development)
-- =============================================

-- Insert sample semester
INSERT INTO Semesters (semesterID, name, startDate, endDate, isActive) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'Fall 2024', '2024-09-01', '2024-12-15', true);

-- Insert sample departments
INSERT INTO Departments (departmentID, name, code) VALUES
('550e8400-e29b-41d4-a716-446655440002', 'Computer Science', 'CS'),
('550e8400-e29b-41d4-a716-446655440003', 'Mathematics', 'MATH'),
('550e8400-e29b-41d4-a716-446655440004', 'Physics', 'PHYS');

-- Insert sample rooms
INSERT INTO Rooms (roomID, roomNumber, maxCapacity, type, assignedDepartmentID) VALUES
('550e8400-e29b-41d4-a716-446655440005', 'CS-101', 50, 'Classroom', '550e8400-e29b-41d4-a716-446655440002'),
('550e8400-e29b-41d4-a716-446655440006', 'CS-LAB1', 30, 'Computer Lab', '550e8400-e29b-41d4-a716-446655440002'),
('550e8400-e29b-41d4-a716-446655440007', 'HALL-A', 200, 'Lecture Hall', NULL);

-- Insert sample instructors
INSERT INTO Instructors (instructorID, name, email, departmentID) VALUES
('550e8400-e29b-41d4-a716-446655440008', 'Dr. John Smith', '<EMAIL>', '550e8400-e29b-41d4-a716-446655440002'),
('550e8400-e29b-41d4-a716-446655440009', 'Dr. Jane Doe', '<EMAIL>', '550e8400-e29b-41d4-a716-446655440003');

-- Insert sample instructor availability
INSERT INTO InstructorAvailability (instructorID, dayOfWeek, availableSlots) VALUES
('550e8400-e29b-41d4-a716-446655440008', 'Monday', '[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]'),
('550e8400-e29b-41d4-a716-446655440008', 'Tuesday', '[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]'),
('550e8400-e29b-41d4-a716-446655440008', 'Wednesday', '[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]'),
('550e8400-e29b-41d4-a716-446655440008', 'Thursday', '[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]'),
('550e8400-e29b-41d4-a716-446655440008', 'Friday', '[1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20]');

-- =============================================
-- TIME SLOT REFERENCE (FOR DOCUMENTATION)
-- =============================================

/*
Time Slot Reference Table (26 slots, 15 minutes each):
Slot | Start Time | End Time
-----|------------|----------
1    | 09:30      | 09:45
2    | 09:45      | 10:00
3    | 10:00      | 10:15
4    | 10:15      | 10:30
5    | 10:30      | 10:45
6    | 10:45      | 11:00
7    | 11:00      | 11:15
8    | 11:15      | 11:30
9    | 11:30      | 11:45
10   | 11:45      | 12:00
11   | 12:00      | 12:15
12   | 12:15      | 12:30
13   | 12:30      | 12:45
14   | 12:45      | 13:00
15   | 13:00      | 13:15
16   | 13:15      | 13:30
17   | 13:30      | 13:45
18   | 13:45      | 14:00
19   | 14:00      | 14:15
20   | 14:15      | 14:30
21   | 14:30      | 14:45
22   | 14:45      | 15:00
23   | 15:00      | 15:15
24   | 15:15      | 15:30
25   | 15:30      | 15:45
26   | 15:45      | 16:00

Working Day: 9:30 AM - 4:00 PM (6.5 hours = 26 slots of 15 minutes each)
*/

-- Student Level table (e.g., "First Year Pharmacy")
CREATE TABLE student_levels (
    level_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(department_id),
    expected_student_count INTEGER NOT NULL CHECK (expected_student_count > 0),
    year_number INTEGER,
    semester_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Group types enum
CREATE TYPE group_type_enum AS ENUM (
    'Full Level',
    'Group',
    'Sub-Group'
);

-- Student Groups table
CREATE TABLE student_groups (
    group_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    level_id UUID NOT NULL REFERENCES student_levels(level_id),
    group_name VARCHAR(100) NOT NULL,
    parent_group_id UUID REFERENCES student_groups(group_id),
    group_type group_type_enum NOT NULL,
    student_count INTEGER NOT NULL CHECK (student_count > 0),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(level_id, group_name)
);

-- Courses table (abstract definition)
CREATE TABLE courses (
    course_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_code VARCHAR(20) NOT NULL,
    title VARCHAR(300) NOT NULL,
    department_id UUID NOT NULL REFERENCES departments(department_id),
    credits INTEGER,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(course_code, department_id)
);

-- Lesson types enum
CREATE TYPE lesson_type_enum AS ENUM (
    'Lecture',
    'Lab',
    'Tutorial',
    'Computer Lab',
    'Seminar',
    'Workshop'
);

-- Lessons table (concrete instances to be scheduled)
CREATE TABLE lessons (
    lesson_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID NOT NULL REFERENCES courses(course_id),
    student_group_id UUID NOT NULL REFERENCES student_groups(group_id),
    instructor_id UUID NOT NULL REFERENCES instructors(instructor_id),
    duration_in_slots INTEGER NOT NULL CHECK (duration_in_slots > 0),
    lesson_type lesson_type_enum NOT NULL,
    required_room_type room_type_enum NOT NULL,
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    sessions_per_week INTEGER DEFAULT 1,
    total_sessions INTEGER,
    priority INTEGER DEFAULT 5, -- 1-10 scale for scheduling priority
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- SCHEDULING TABLES
-- =============================================

-- Days of week enum
CREATE TYPE day_of_week_enum AS ENUM (
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday',
    'Saturday',
    'Sunday'
);

-- Schedule entries (the actual timetable)
CREATE TABLE schedule_entries (
    entry_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    lesson_id UUID NOT NULL REFERENCES lessons(lesson_id),
    room_id UUID NOT NULL REFERENCES rooms(room_id),
    day_of_week day_of_week_enum NOT NULL,
    start_slot INTEGER NOT NULL CHECK (start_slot >= 1 AND start_slot <= 26),
    end_slot INTEGER NOT NULL CHECK (end_slot >= 1 AND end_slot <= 26),
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    week_number INTEGER, -- For recurring schedules
    is_locked BOOLEAN DEFAULT FALSE, -- Prevents auto-rescheduling
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CHECK (end_slot > start_slot)
);

-- =============================================
-- CONSTRAINT MANAGEMENT
-- =============================================

-- Constraint types enum
CREATE TYPE constraint_type_enum AS ENUM (
    'HARD',
    'SOFT'
);

-- Constraint categories enum
CREATE TYPE constraint_category_enum AS ENUM (
    'CONTIGUITY',
    'TIME_PLACEMENT',
    'RESOURCE_USAGE',
    'COMBINATION'
);

-- Scheduling constraints table
CREATE TABLE scheduling_constraints (
    constraint_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    constraint_type constraint_type_enum NOT NULL,
    constraint_category constraint_category_enum NOT NULL,
    rule_definition JSONB NOT NULL, -- Flexible JSON structure for different rule types
    weight INTEGER DEFAULT 1, -- For soft constraints
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- SUBSTITUTION MANAGEMENT
-- =============================================

-- Substitution status enum
CREATE TYPE substitution_status_enum AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED',
    'COMPLETED'
);

-- Daily substitutions table
CREATE TABLE substitutions (
    substitution_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    original_entry_id UUID NOT NULL REFERENCES schedule_entries(entry_id),
    absent_instructor_id UUID NOT NULL REFERENCES instructors(instructor_id),
    substitute_instructor_id UUID REFERENCES instructors(instructor_id),
    substitution_date DATE NOT NULL,
    reason TEXT,
    status substitution_status_enum DEFAULT 'PENDING',
    notes TEXT,
    created_by UUID, -- User who created the substitution
    approved_by UUID, -- User who approved the substitution
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- WORKFLOW MANAGEMENT
-- =============================================

-- Generation status enum
CREATE TYPE generation_status_enum AS ENUM (
    'NOT_STARTED',
    'DATA_INPUT',
    'MANIFEST_GENERATED',
    'MANIFEST_VALIDATED',
    'SCHEDULING_IN_PROGRESS',
    'SCHEDULING_COMPLETED',
    'SCHEDULING_FAILED',
    'PUBLISHED'
);

-- Schedule generation sessions
CREATE TABLE generation_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    semester_id UUID NOT NULL REFERENCES semesters(semester_id),
    status generation_status_enum DEFAULT 'NOT_STARTED',
    manifest_file_path VARCHAR(500),
    generation_started_at TIMESTAMP,
    generation_completed_at TIMESTAMP,
    total_lessons INTEGER,
    scheduled_lessons INTEGER,
    unscheduled_lessons INTEGER,
    constraint_violations JSONB,
    error_log TEXT,
    created_by UUID,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Core entity indexes
CREATE INDEX idx_instructors_department ON instructors(department_id);
CREATE INDEX idx_instructors_active ON instructors(is_active);
CREATE INDEX idx_rooms_type ON rooms(type);
CREATE INDEX idx_rooms_department ON rooms(assigned_department_id);
CREATE INDEX idx_student_groups_level ON student_groups(level_id);
CREATE INDEX idx_student_groups_parent ON student_groups(parent_group_id);
CREATE INDEX idx_lessons_course ON lessons(course_id);
CREATE INDEX idx_lessons_group ON lessons(student_group_id);
CREATE INDEX idx_lessons_instructor ON lessons(instructor_id);
CREATE INDEX idx_lessons_semester ON lessons(semester_id);

-- Scheduling indexes
CREATE INDEX idx_schedule_entries_lesson ON schedule_entries(lesson_id);
CREATE INDEX idx_schedule_entries_room ON schedule_entries(room_id);
CREATE INDEX idx_schedule_entries_day_slot ON schedule_entries(day_of_week, start_slot, end_slot);
CREATE INDEX idx_schedule_entries_semester ON schedule_entries(semester_id);

-- Substitution indexes
CREATE INDEX idx_substitutions_date ON substitutions(substitution_date);
CREATE INDEX idx_substitutions_original_instructor ON substitutions(absent_instructor_id);
CREATE INDEX idx_substitutions_substitute ON substitutions(substitute_instructor_id);
CREATE INDEX idx_substitutions_status ON substitutions(status);

-- =============================================
-- FOREIGN KEY CONSTRAINTS
-- =============================================

ALTER TABLE departments ADD CONSTRAINT fk_departments_head
    FOREIGN KEY (head_instructor_id) REFERENCES instructors(instructor_id);

-- =============================================
-- TIME SLOT REFERENCE (FOR DOCUMENTATION)
-- =============================================

-- Time slots mapping (1-26):
-- Slot 1: 09:30-09:45, Slot 2: 09:45-10:00, ..., Slot 26: 15:45-16:00
-- This mapping is implemented in application logic, not stored in database

/*
Time Slot Reference Table:
Slot | Start Time | End Time
-----|------------|----------
1    | 09:30      | 09:45
2    | 09:45      | 10:00
3    | 10:00      | 10:15
4    | 10:15      | 10:30
5    | 10:30      | 10:45
6    | 10:45      | 11:00
7    | 11:00      | 11:15
8    | 11:15      | 11:30
9    | 11:30      | 11:45
10   | 11:45      | 12:00
11   | 12:00      | 12:15
12   | 12:15      | 12:30
13   | 12:30      | 12:45
14   | 12:45      | 13:00
15   | 13:00      | 13:15
16   | 13:15      | 13:30
17   | 13:30      | 13:45
18   | 13:45      | 14:00
19   | 14:00      | 14:15
20   | 14:15      | 14:30
21   | 14:30      | 14:45
22   | 14:45      | 15:00
23   | 15:00      | 15:15
24   | 15:15      | 15:30
25   | 15:30      | 15:45
26   | 15:45      | 16:00
*/