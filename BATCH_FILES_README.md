# University Schedule Assistant - Windows Batch Files

## Overview
This directory contains Windows batch files that automate the setup, startup, management, and maintenance of the University Schedule Assistant application on Windows systems.

## Batch Files

### 🚀 `start-application.bat` - Main Startup Script
**Purpose**: Complete application startup with dependency checking and setup

**Features**:
- ✅ Checks for required tools (<PERSON>er, Node.js, npm)
- ✅ Creates/validates .env configuration file
- ✅ Creates necessary directories
- ✅ Starts Docker Desktop if not running
- ✅ Installs backend and frontend dependencies
- ✅ Builds and starts all services
- ✅ Runs database migrations
- ✅ Performs health checks
- ✅ Opens application in browser (optional)
- ✅ Shows live logs (optional)

**Usage**:
```cmd
start-application.bat
```

**What it does**:
1. Validates system prerequisites
2. Sets up environment configuration
3. Creates required directories
4. Ensures Docker is running
5. Installs all dependencies
6. Builds and starts services
7. Waits for services to be ready
8. Runs database setup
9. Performs final health checks
10. Offers to open browser and show logs

### 🛑 `stop-application.bat` - Application Stop Script
**Purpose**: Safely stops all application services

**Features**:
- ✅ Gracefully stops all Docker containers
- ✅ Shows final service status
- ✅ Optional cleanup of unused Docker resources
- ✅ Clear status reporting

**Usage**:
```cmd
stop-application.bat
```

### 📊 `check-status.bat` - Status Monitoring Script
**Purpose**: Comprehensive status check of all services

**Features**:
- ✅ Docker service status
- ✅ Container status and health
- ✅ Service health checks (Backend, Frontend, Database, Redis)
- ✅ Resource usage monitoring
- ✅ Recent log display
- ✅ Application URLs
- ✅ Management command reference

**Usage**:
```cmd
check-status.bat
```

### 💾 `backup-data.bat` - Data Backup Script
**Purpose**: Creates comprehensive backups of application data

**Features**:
- ✅ Database backup (PostgreSQL dump)
- ✅ Uploads directory backup
- ✅ Logs backup
- ✅ Configuration files backup
- ✅ Backup compression (optional)
- ✅ Automatic cleanup of old backups
- ✅ Backup information file
- ✅ Restore instructions

**Usage**:
```cmd
backup-data.bat
```

### 🔄 `refresh-browser.bat` - Browser Cache Refresh Script
**Purpose**: Clears browser cache and opens fresh application instance

**Features**:
- ✅ Clears browser cache automatically
- ✅ Checks if application is running
- ✅ Opens fresh browser instance with cache-busting
- ✅ Provides refresh instructions
- ✅ Handles old version display issues

**Usage**:
```cmd
refresh-browser.bat
```

## Prerequisites

### Required Software
- **Windows 10/11** or **Windows Server 2019+**
- **Docker Desktop** for Windows
- **Node.js** (version 16 or higher)
- **Git** (for cloning repository)

### Installation Links
- [Docker Desktop](https://www.docker.com/products/docker-desktop)
- [Node.js](https://nodejs.org/)
- [Git for Windows](https://git-scm.com/download/win)

## Quick Start Guide

### 1. First-Time Setup
```cmd
# Clone the repository
git clone https://github.com/your-org/schedule-assistant.git
cd schedule-assistant

# Run the startup script
start-application.bat
```

### 2. Daily Operations
```cmd
# Start the application
start-application.bat

# Check application status
check-status.bat

# Stop the application
stop-application.bat

# Create a backup
backup-data.bat
```

## Detailed Usage Instructions

### Starting the Application

1. **Double-click** `start-application.bat` or run from command prompt
2. **Follow prompts** for environment configuration
3. **Wait for setup** to complete (may take 5-10 minutes on first run)
4. **Access application** at http://localhost:3000

**First Run Process**:
- Creates `.env` file from template
- Opens `.env` for editing (configure your passwords)
- Downloads Docker images
- Installs Node.js dependencies
- Builds application containers
- Sets up database
- Starts all services

### Monitoring the Application

Run `check-status.bat` to see:
- Service health status
- Resource usage
- Recent logs
- Application URLs
- Management commands

### Stopping the Application

Run `stop-application.bat` to:
- Gracefully stop all services
- Show final status
- Optionally clean up Docker resources

### Creating Backups

Run `backup-data.bat` to:
- Backup database to SQL file
- Backup uploaded files
- Backup application logs
- Backup configuration files
- Create timestamped backup folder
- Optionally compress backup

## Configuration

### Environment Variables (.env file)
The startup script will create and open a `.env` file for configuration:

```env
# Database Configuration
POSTGRES_DB=schedule_assistant
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_secure_password_here

# Redis Configuration
REDIS_PASSWORD=your_redis_password_here

# Application Security
JWT_SECRET=your_very_secure_jwt_secret_key_here_minimum_32_characters
SESSION_SECRET=your_secure_session_secret_here

# Application URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
```

**Important**: Change the default passwords before first use!