@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Test Startup Script
:: This script tests the startup process step by step

echo ========================================
echo University Schedule Assistant - Test
echo ========================================
echo.

:: Set color for better visibility
color 0F

echo [INFO] Testing University Schedule Assistant startup...
echo.

:: Stop any existing containers
echo [INFO] Stopping any existing containers...
docker-compose down
echo.

:: Check if .env file exists
if not exist ".env" (
    echo [ERROR] .env file not found
    echo [INFO] Please run start-application.bat first to create .env file
    pause
    exit /b 1
)
echo [SUCCESS] .env file found
echo.

:: Test database startup
echo [INFO] Testing database startup...
docker-compose up -d postgres
timeout /t 10 /nobreak >nul
docker-compose ps postgres
echo.

:: Test Redis startup
echo [INFO] Testing Redis startup...
docker-compose up -d redis
timeout /t 5 /nobreak >nul
docker-compose ps redis
echo.

:: Test API build and startup
echo [INFO] Testing API build and startup...
docker-compose build api
if %errorLevel% neq 0 (
    echo [ERROR] API build failed
    echo [INFO] Checking build logs...
    docker-compose logs api
    pause
    exit /b 1
)
echo [SUCCESS] API build completed

docker-compose up -d api
timeout /t 15 /nobreak >nul
docker-compose ps api
echo.

:: Check API logs
echo [INFO] API logs:
docker-compose logs --tail=10 api
echo.

:: Test frontend build and startup
echo [INFO] Testing frontend build and startup...
docker-compose build frontend
if %errorLevel% neq 0 (
    echo [ERROR] Frontend build failed
    echo [INFO] Checking build logs...
    docker-compose logs frontend
    pause
    exit /b 1
)
echo [SUCCESS] Frontend build completed

docker-compose up -d frontend
timeout /t 15 /nobreak >nul
docker-compose ps frontend
echo.

:: Check frontend logs
echo [INFO] Frontend logs:
docker-compose logs --tail=10 frontend
echo.

:: Show all container status
echo [INFO] All container status:
docker-compose ps
echo.

:: Test health endpoints
echo [INFO] Testing health endpoints...
timeout /t 5 /nobreak >nul

curl -f http://localhost:3001/health >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Backend health check passed
) else (
    echo [WARNING] Backend health check failed
)

powershell -command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5 -UseBasicParsing; exit 0 } catch { exit 1 }" >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Frontend health check passed
) else (
    echo [WARNING] Frontend health check failed
)

echo.
echo [INFO] Test completed!
echo [INFO] Check the logs above for any errors
pause