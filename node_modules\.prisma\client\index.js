
Object.defineProperty(exports, "__esModule", { value: true });

const {
  PrismaClientKnownRequestError,
  PrismaClientUnknownRequestError,
  PrismaClientRustPanicError,
  PrismaClientInitializationError,
  PrismaClientValidationError,
  NotFoundError,
  getPrismaClient,
  sqltag,
  empty,
  join,
  raw,
  skip,
  Decimal,
  Debug,
  objectEnumValues,
  makeStrictEnum,
  Extensions,
  warnOnce,
  defineDmmfProperty,
  Public,
  getRuntime
} = require('@prisma/client/runtime/library.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = PrismaClientKnownRequestError;
Prisma.PrismaClientUnknownRequestError = PrismaClientUnknownRequestError
Prisma.PrismaClientRustPanicError = PrismaClientRustPanicError
Prisma.PrismaClientInitializationError = PrismaClientInitializationError
Prisma.PrismaClientValidationError = PrismaClientValidationError
Prisma.NotFoundError = NotFoundError
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = sqltag
Prisma.empty = empty
Prisma.join = join
Prisma.raw = raw
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = Extensions.getExtensionContext
Prisma.defineExtension = Extensions.defineExtension

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}




  const path = require('path')

/**
 * Enums
 */
exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.SemesterScalarFieldEnum = {
  semesterID: 'semesterID',
  name: 'name',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DepartmentScalarFieldEnum = {
  departmentID: 'departmentID',
  name: 'name',
  code: 'code',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoomScalarFieldEnum = {
  roomID: 'roomID',
  roomNumber: 'roomNumber',
  maxCapacity: 'maxCapacity',
  type: 'type',
  assignedDepartmentID: 'assignedDepartmentID',
  building: 'building',
  floorNumber: 'floorNumber',
  equipmentNotes: 'equipmentNotes',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InstructorScalarFieldEnum = {
  instructorID: 'instructorID',
  name: 'name',
  email: 'email',
  departmentID: 'departmentID',
  maxDailySlots: 'maxDailySlots',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InstructorAvailabilityScalarFieldEnum = {
  availabilityID: 'availabilityID',
  instructorID: 'instructorID',
  dayOfWeek: 'dayOfWeek',
  availableSlots: 'availableSlots',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentLevelScalarFieldEnum = {
  levelID: 'levelID',
  name: 'name',
  departmentID: 'departmentID',
  expectedStudentCount: 'expectedStudentCount',
  yearNumber: 'yearNumber',
  semesterNumber: 'semesterNumber',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentGroupScalarFieldEnum = {
  groupID: 'groupID',
  levelID: 'levelID',
  groupName: 'groupName',
  parentGroupID: 'parentGroupID',
  groupType: 'groupType',
  studentCount: 'studentCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseScalarFieldEnum = {
  courseID: 'courseID',
  courseCode: 'courseCode',
  title: 'title',
  departmentID: 'departmentID',
  credits: 'credits',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonScalarFieldEnum = {
  lessonID: 'lessonID',
  courseID: 'courseID',
  studentGroupID: 'studentGroupID',
  instructorID: 'instructorID',
  durationInSlots: 'durationInSlots',
  lessonType: 'lessonType',
  requiredRoomType: 'requiredRoomType',
  semesterID: 'semesterID',
  sessionsPerWeek: 'sessionsPerWeek',
  totalSessions: 'totalSessions',
  priority: 'priority',
  specialRequirements: 'specialRequirements',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ScheduledLessonScalarFieldEnum = {
  scheduledLessonID: 'scheduledLessonID',
  lessonID: 'lessonID',
  roomID: 'roomID',
  dayOfWeek: 'dayOfWeek',
  startSlot: 'startSlot',
  endSlot: 'endSlot',
  semesterID: 'semesterID',
  weekNumber: 'weekNumber',
  status: 'status',
  lockedBy: 'lockedBy',
  lockedAt: 'lockedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubstitutionScalarFieldEnum = {
  substitutionID: 'substitutionID',
  originalInstructorID: 'originalInstructorID',
  replacementInstructorID: 'replacementInstructorID',
  scheduledLessonID: 'scheduledLessonID',
  dateOfAbsence: 'dateOfAbsence',
  reason: 'reason',
  status: 'status',
  notes: 'notes',
  requestedBy: 'requestedBy',
  approvedBy: 'approvedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PublishedScheduleScalarFieldEnum = {
  publishedScheduleID: 'publishedScheduleID',
  semesterID: 'semesterID',
  scheduleData: 'scheduleData',
  publishedAt: 'publishedAt',
  publishedBy: 'publishedBy',
  version: 'version',
  isActive: 'isActive',
  notes: 'notes'
};

exports.Prisma.SchedulingConstraintScalarFieldEnum = {
  constraintID: 'constraintID',
  name: 'name',
  description: 'description',
  constraintType: 'constraintType',
  ruleDefinition: 'ruleDefinition',
  weight: 'weight',
  isActive: 'isActive',
  semesterID: 'semesterID',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.GenerationSessionScalarFieldEnum = {
  sessionID: 'sessionID',
  semesterID: 'semesterID',
  status: 'status',
  manifestFilePath: 'manifestFilePath',
  jobID: 'jobID',
  generationStartedAt: 'generationStartedAt',
  generationCompletedAt: 'generationCompletedAt',
  totalLessons: 'totalLessons',
  scheduledLessons: 'scheduledLessons',
  unscheduledLessons: 'unscheduledLessons',
  qualityScore: 'qualityScore',
  constraintViolations: 'constraintViolations',
  errorLog: 'errorLog',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  userID: 'userID',
  username: 'username',
  email: 'email',
  passwordHash: 'passwordHash',
  role: 'role',
  departmentID: 'departmentID',
  instructorID: 'instructorID',
  isActive: 'isActive',
  lastLogin: 'lastLogin',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.RoomType = exports.$Enums.RoomType = {
  LECTURE_HALL: 'LECTURE_HALL',
  CLASSROOM: 'CLASSROOM',
  LAB: 'LAB',
  TUTORIAL_ROOM: 'TUTORIAL_ROOM',
  COMPUTER_LAB: 'COMPUTER_LAB'
};

exports.GroupType = exports.$Enums.GroupType = {
  FULL_LEVEL: 'FULL_LEVEL',
  GROUP: 'GROUP',
  SUB_GROUP: 'SUB_GROUP'
};

exports.LessonType = exports.$Enums.LessonType = {
  LECTURE: 'LECTURE',
  LAB: 'LAB',
  TUTORIAL: 'TUTORIAL',
  COMPUTER_LAB: 'COMPUTER_LAB',
  SEMINAR: 'SEMINAR',
  WORKSHOP: 'WORKSHOP'
};

exports.DayOfWeek = exports.$Enums.DayOfWeek = {
  MONDAY: 'MONDAY',
  TUESDAY: 'TUESDAY',
  WEDNESDAY: 'WEDNESDAY',
  THURSDAY: 'THURSDAY',
  FRIDAY: 'FRIDAY',
  SATURDAY: 'SATURDAY',
  SUNDAY: 'SUNDAY'
};

exports.ScheduleStatus = exports.$Enums.ScheduleStatus = {
  SCHEDULED: 'SCHEDULED',
  LOCKED: 'LOCKED',
  CANCELLED: 'CANCELLED',
  MODIFIED: 'MODIFIED'
};

exports.SubstitutionStatus = exports.$Enums.SubstitutionStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  COMPLETED: 'COMPLETED'
};

exports.ConstraintType = exports.$Enums.ConstraintType = {
  HARD: 'HARD',
  SOFT: 'SOFT',
  ADVANCED: 'ADVANCED'
};

exports.GenerationStatus = exports.$Enums.GenerationStatus = {
  NOT_STARTED: 'NOT_STARTED',
  DATA_INPUT: 'DATA_INPUT',
  MANIFEST_GENERATED: 'MANIFEST_GENERATED',
  MANIFEST_VALIDATED: 'MANIFEST_VALIDATED',
  SCHEDULING_IN_PROGRESS: 'SCHEDULING_IN_PROGRESS',
  SCHEDULING_COMPLETED: 'SCHEDULING_COMPLETED',
  SCHEDULING_FAILED: 'SCHEDULING_FAILED',
  PUBLISHED: 'PUBLISHED'
};

exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  SCHEDULER: 'SCHEDULER',
  DEPARTMENT_COORDINATOR: 'DEPARTMENT_COORDINATOR',
  INSTRUCTOR: 'INSTRUCTOR',
  STUDENT: 'STUDENT'
};

exports.Prisma.ModelName = {
  Semester: 'Semester',
  Department: 'Department',
  Room: 'Room',
  Instructor: 'Instructor',
  InstructorAvailability: 'InstructorAvailability',
  StudentLevel: 'StudentLevel',
  StudentGroup: 'StudentGroup',
  Course: 'Course',
  Lesson: 'Lesson',
  ScheduledLesson: 'ScheduledLesson',
  Substitution: 'Substitution',
  PublishedSchedule: 'PublishedSchedule',
  SchedulingConstraint: 'SchedulingConstraint',
  GenerationSession: 'GenerationSession',
  User: 'User'
};
/**
 * Create the Client
 */
const config = {
  "generator": {
    "name": "client",
    "provider": {
      "fromEnvVar": null,
      "value": "prisma-client-js"
    },
    "output": {
      "value": "D:\\HUE\\DEVELOPED SOFTWARE\\Schedule_assisstnant\\node_modules\\@prisma\\client",
      "fromEnvVar": null
    },
    "config": {
      "engineType": "library"
    },
    "binaryTargets": [
      {
        "fromEnvVar": null,
        "value": "windows",
        "native": true
      }
    ],
    "previewFeatures": [],
    "sourceFilePath": "D:\\HUE\\DEVELOPED SOFTWARE\\Schedule_assisstnant\\prisma\\schema.prisma"
  },
  "relativeEnvPaths": {
    "rootEnvPath": null,
    "schemaEnvPath": "../../../.env"
  },
  "relativePath": "../../../prisma",
  "clientVersion": "5.22.0",
  "engineVersion": "605197351a3c8bdd595af2d2a9bc3025bca48ea2",
  "datasourceNames": [
    "db"
  ],
  "activeProvider": "postgresql",
  "postinstall": true,
  "inlineDatasources": {
    "db": {
      "url": {
        "fromEnvVar": "DATABASE_URL",
        "value": null
      }
    }
  },
  "inlineSchema": "// University Schedule Assistant - Prisma Schema\n// This schema corresponds to the Phase 1 database implementation\n\ngenerator client {\n  provider = \"prisma-client-js\"\n}\n\ndatasource db {\n  provider = \"postgresql\"\n  url      = env(\"DATABASE_URL\")\n}\n\n// =============================================\n// ENUMS\n// =============================================\n\nenum RoomType {\n  LECTURE_HALL  @map(\"Lecture Hall\")\n  CLASSROOM     @map(\"Classroom\")\n  LAB           @map(\"Lab\")\n  TUTORIAL_ROOM @map(\"Tutorial Room\")\n  COMPUTER_LAB  @map(\"Computer Lab\")\n\n  @@map(\"room_type_enum\")\n}\n\nenum GroupType {\n  FULL_LEVEL @map(\"Full Level\")\n  GROUP      @map(\"Group\")\n  SUB_GROUP  @map(\"Sub-Group\")\n\n  @@map(\"group_type_enum\")\n}\n\nenum LessonType {\n  LECTURE      @map(\"Lecture\")\n  LAB          @map(\"Lab\")\n  TUTORIAL     @map(\"Tutorial\")\n  COMPUTER_LAB @map(\"Computer Lab\")\n  SEMINAR      @map(\"Seminar\")\n  WORKSHOP     @map(\"Workshop\")\n\n  @@map(\"lesson_type_enum\")\n}\n\nenum ScheduleStatus {\n  SCHEDULED @map(\"Scheduled\")\n  LOCKED    @map(\"Locked\")\n  CANCELLED @map(\"Cancelled\")\n  MODIFIED  @map(\"Modified\")\n\n  @@map(\"schedule_status_enum\")\n}\n\nenum DayOfWeek {\n  MONDAY    @map(\"Monday\")\n  TUESDAY   @map(\"Tuesday\")\n  WEDNESDAY @map(\"Wednesday\")\n  THURSDAY  @map(\"Thursday\")\n  FRIDAY    @map(\"Friday\")\n  SATURDAY  @map(\"Saturday\")\n  SUNDAY    @map(\"Sunday\")\n\n  @@map(\"day_of_week_enum\")\n}\n\nenum SubstitutionStatus {\n  PENDING   @map(\"Pending\")\n  APPROVED  @map(\"Approved\")\n  REJECTED  @map(\"Rejected\")\n  COMPLETED @map(\"Completed\")\n\n  @@map(\"substitution_status_enum\")\n}\n\nenum ConstraintType {\n  HARD     @map(\"Hard\")\n  SOFT     @map(\"Soft\")\n  ADVANCED @map(\"Advanced\")\n\n  @@map(\"constraint_type_enum\")\n}\n\nenum GenerationStatus {\n  NOT_STARTED            @map(\"NOT_STARTED\")\n  DATA_INPUT             @map(\"DATA_INPUT\")\n  MANIFEST_GENERATED     @map(\"MANIFEST_GENERATED\")\n  MANIFEST_VALIDATED     @map(\"MANIFEST_VALIDATED\")\n  SCHEDULING_IN_PROGRESS @map(\"SCHEDULING_IN_PROGRESS\")\n  SCHEDULING_COMPLETED   @map(\"SCHEDULING_COMPLETED\")\n  SCHEDULING_FAILED      @map(\"SCHEDULING_FAILED\")\n  PUBLISHED              @map(\"PUBLISHED\")\n\n  @@map(\"generation_status_enum\")\n}\n\nenum UserRole {\n  ADMIN                  @map(\"admin\")\n  SCHEDULER              @map(\"scheduler\")\n  DEPARTMENT_COORDINATOR @map(\"department_coordinator\")\n  INSTRUCTOR             @map(\"instructor\")\n  STUDENT                @map(\"student\")\n\n  @@map(\"user_role_enum\")\n}\n\n// =============================================\n// CORE ENTITIES\n// =============================================\n\nmodel Semester {\n  semesterID String   @id @default(uuid()) @db.Uuid\n  name       String   @db.VarChar(100)\n  startDate  DateTime @db.Date\n  endDate    DateTime @db.Date\n  isActive   Boolean  @default(false)\n  createdAt  DateTime @default(now()) @map(\"created_at\")\n  updatedAt  DateTime @updatedAt @map(\"updated_at\")\n\n  // Relations\n  lessons            Lesson[]\n  scheduledLessons   ScheduledLesson[]\n  publishedSchedules PublishedSchedule[]\n  generationSessions GenerationSession[]\n  constraints        SchedulingConstraint[]\n\n  @@map(\"Semesters\")\n}\n\nmodel Department {\n  departmentID String   @id @default(uuid()) @db.Uuid\n  name         String   @unique @db.VarChar(200)\n  code         String?  @unique @db.VarChar(10)\n  createdAt    DateTime @default(now()) @map(\"created_at\")\n  updatedAt    DateTime @updatedAt @map(\"updated_at\")\n\n  // Relations\n  instructors   Instructor[]\n  rooms         Room[]\n  studentLevels StudentLevel[]\n  courses       Course[]\n  users         User[]\n\n  @@map(\"Departments\")\n}\n\nmodel Room {\n  roomID               String   @id @default(uuid()) @db.Uuid\n  roomNumber           String   @unique @db.VarChar(50)\n  maxCapacity          Int\n  type                 RoomType\n  assignedDepartmentID String?  @db.Uuid\n  building             String?  @db.VarChar(100)\n  floorNumber          Int?     @map(\"floor_number\")\n  equipmentNotes       String?  @map(\"equipment_notes\")\n  isActive             Boolean  @default(true)\n  createdAt            DateTime @default(now()) @map(\"created_at\")\n  updatedAt            DateTime @updatedAt @map(\"updated_at\")\n\n  // Relations\n  assignedDepartment Department?       @relation(fields: [assignedDepartmentID], references: [departmentID])\n  scheduledLessons   ScheduledLesson[]\n\n  @@map(\"Rooms\")\n}\n\nmodel Instructor {\n  instructorID  String   @id @default(uuid()) @db.Uuid\n  name          String   @db.VarChar(200)\n  email         String?  @unique @db.VarChar(255)\n  departmentID  String   @db.Uuid\n  maxDailySlots Int      @default(20)\n  isActive      Boolean  @default(true)\n  createdAt     DateTime @default(now()) @map(\"created_at\")\n  updatedAt     DateTime @updatedAt @map(\"updated_at\")\n\n  // Relations\n  department               Department               @relation(fields: [departmentID], references: [departmentID])\n  availability             InstructorAvailability[]\n  lessons                  Lesson[]\n  originalSubstitutions    Substitution[]           @relation(\"OriginalInstructor\")\n  replacementSubstitutions Substitution[]           @relation(\"ReplacementInstructor\")\n  users                    User[]\n\n  @@map(\"Instructors\")\n}\n\nmodel InstructorAvailability {\n  availabilityID String   @id @default(uuid()) @db.Uuid\n  instructorID   String   @db.Uuid\n  dayOfWeek      String   @db.VarChar(10)\n  availableSlots Json     @default(\"[]\") @db.JsonB\n  createdAt      DateTime @default(now()) @map(\"created_at\")\n  updatedAt      DateTime @updatedAt @map(\"updated_at\")\n\n  // Relations\n  instructor Instructor @relation(fields: [instructorID], references: [instructorID], onDelete: Cascade)\n\n  @@unique([instructorID, dayOfWeek])\n  @@map(\"InstructorAvailability\")\n}\n\nmodel StudentLevel {\n  levelID              String   @id @default(uuid()) @db.Uuid\n  name                 String   @db.VarChar(200)\n  departmentID         String   @db.Uuid\n  expectedStudentCount Int\n  yearNumber           Int?\n  semesterNumber       Int?\n  createdAt            DateTime @default(now()) @map(\"created_at\")\n  updatedAt            DateTime @updatedAt @map(\"updated_at\")\n\n  // Relations\n  department    Department     @relation(fields: [departmentID], references: [departmentID])\n  studentGroups StudentGroup[]\n\n  @@map(\"StudentLevels\")\n}\n\nmodel StudentGroup {\n  groupID       String    @id @default(uuid()) @db.Uuid\n  levelID       String    @db.Uuid\n  groupName     String    @db.VarChar(100)\n  parentGroupID String?   @db.Uuid\n  groupType     GroupType\n  studentCount  Int\n  createdAt     DateTime  @default(now()) @map(\"created_at\")\n  updatedAt     DateTime  @updatedAt @map(\"updated_at\")\n\n  // Relations\n  level       StudentLevel   @relation(fields: [levelID], references: [levelID])\n  parentGroup StudentGroup?  @relation(\"GroupHierarchy\", fields: [parentGroupID], references: [groupID])\n  subGroups   StudentGroup[] @relation(\"GroupHierarchy\")\n  lessons     Lesson[]\n\n  @@unique([levelID, groupName])\n  @@map(\"StudentGroups\")\n}\n\nmodel Course {\n  courseID     String   @id @default(uuid()) @db.Uuid\n  courseCode   String   @db.VarChar(20)\n  title        String   @db.VarChar(300)\n  departmentID String   @db.Uuid\n  credits      Int?\n  description  String?\n  isActive     Boolean  @default(true)\n  createdAt    DateTime @default(now()) @map(\"created_at\")\n  updatedAt    DateTime @updatedAt @map(\"updated_at\")\n\n  // Relations\n  department Department @relation(fields: [departmentID], references: [departmentID])\n  lessons    Lesson[]\n\n  @@unique([courseCode, departmentID])\n  @@map(\"Courses\")\n}\n\nmodel Lesson {\n  lessonID            String     @id @default(uuid()) @db.Uuid\n  courseID            String     @db.Uuid\n  studentGroupID      String     @db.Uuid\n  instructorID        String     @db.Uuid\n  durationInSlots     Int\n  lessonType          LessonType\n  requiredRoomType    RoomType\n  semesterID          String     @db.Uuid\n  sessionsPerWeek     Int        @default(1)\n  totalSessions       Int?\n  priority            Int        @default(5)\n  specialRequirements String?\n  notes               String?\n  createdAt           DateTime   @default(now()) @map(\"created_at\")\n  updatedAt           DateTime   @updatedAt @map(\"updated_at\")\n\n  // Relations\n  course           Course            @relation(fields: [courseID], references: [courseID])\n  studentGroup     StudentGroup      @relation(fields: [studentGroupID], references: [groupID])\n  instructor       Instructor        @relation(fields: [instructorID], references: [instructorID])\n  semester         Semester          @relation(fields: [semesterID], references: [semesterID])\n  scheduledLessons ScheduledLesson[]\n\n  @@map(\"Lessons\")\n}\n\n// =============================================\n// OUTPUT & OPERATIONAL TABLES\n// =============================================\n\nmodel ScheduledLesson {\n  scheduledLessonID String         @id @default(uuid()) @db.Uuid\n  lessonID          String         @db.Uuid\n  roomID            String         @db.Uuid\n  dayOfWeek         DayOfWeek\n  startSlot         Int\n  endSlot           Int\n  semesterID        String         @db.Uuid\n  weekNumber        Int            @default(1)\n  status            ScheduleStatus @default(SCHEDULED)\n  lockedBy          String?        @db.Uuid\n  lockedAt          DateTime?\n  createdAt         DateTime       @default(now()) @map(\"created_at\")\n  updatedAt         DateTime       @updatedAt @map(\"updated_at\")\n\n  // Relations\n  lesson        Lesson         @relation(fields: [lessonID], references: [lessonID])\n  room          Room           @relation(fields: [roomID], references: [roomID])\n  semester      Semester       @relation(fields: [semesterID], references: [semesterID])\n  substitutions Substitution[]\n\n  @@unique([roomID, dayOfWeek, startSlot, endSlot, semesterID, weekNumber])\n  @@unique([lessonID, dayOfWeek, startSlot, endSlot, semesterID, weekNumber])\n  @@map(\"ScheduledLessons\")\n}\n\nmodel Substitution {\n  substitutionID          String             @id @default(uuid()) @db.Uuid\n  originalInstructorID    String             @db.Uuid\n  replacementInstructorID String?            @db.Uuid\n  scheduledLessonID       String             @db.Uuid\n  dateOfAbsence           DateTime           @db.Date\n  reason                  String?\n  status                  SubstitutionStatus @default(PENDING)\n  notes                   String?\n  requestedBy             String?            @db.Uuid\n  approvedBy              String?            @db.Uuid\n  createdAt               DateTime           @default(now()) @map(\"created_at\")\n  updatedAt               DateTime           @updatedAt @map(\"updated_at\")\n\n  // Relations\n  originalInstructor    Instructor      @relation(\"OriginalInstructor\", fields: [originalInstructorID], references: [instructorID])\n  replacementInstructor Instructor?     @relation(\"ReplacementInstructor\", fields: [replacementInstructorID], references: [instructorID])\n  scheduledLesson       ScheduledLesson @relation(fields: [scheduledLessonID], references: [scheduledLessonID])\n\n  @@map(\"Substitutions\")\n}\n\nmodel PublishedSchedule {\n  publishedScheduleID String   @id @default(uuid()) @db.Uuid\n  semesterID          String   @db.Uuid\n  scheduleData        Json     @db.JsonB\n  publishedAt         DateTime @default(now())\n  publishedBy         String?  @db.Uuid\n  version             Int      @default(1)\n  isActive            Boolean  @default(true)\n  notes               String?\n\n  // Relations\n  semester Semester @relation(fields: [semesterID], references: [semesterID])\n\n  @@map(\"PublishedSchedules\")\n}\n\nmodel SchedulingConstraint {\n  constraintID   String         @id @default(uuid()) @db.Uuid\n  name           String         @db.VarChar(200)\n  description    String?\n  constraintType ConstraintType\n  ruleDefinition Json           @db.JsonB\n  weight         Int            @default(1)\n  isActive       Boolean        @default(true)\n  semesterID     String?        @db.Uuid\n  createdAt      DateTime       @default(now()) @map(\"created_at\")\n  updatedAt      DateTime       @updatedAt @map(\"updated_at\")\n\n  // Relations\n  semester Semester? @relation(fields: [semesterID], references: [semesterID])\n\n  @@map(\"SchedulingConstraints\")\n}\n\nmodel GenerationSession {\n  sessionID             String           @id @default(uuid()) @db.Uuid\n  semesterID            String           @db.Uuid\n  status                GenerationStatus @default(NOT_STARTED)\n  manifestFilePath      String?          @db.VarChar(500)\n  jobID                 String?          @db.VarChar(100)\n  generationStartedAt   DateTime?\n  generationCompletedAt DateTime?\n  totalLessons          Int?\n  scheduledLessons      Int?\n  unscheduledLessons    Int?\n  qualityScore          Decimal?         @db.Decimal(5, 2)\n  constraintViolations  Json?            @db.JsonB\n  errorLog              String?\n  createdBy             String?          @db.Uuid\n  createdAt             DateTime         @default(now()) @map(\"created_at\")\n  updatedAt             DateTime         @updatedAt @map(\"updated_at\")\n\n  // Relations\n  semester Semester @relation(fields: [semesterID], references: [semesterID])\n\n  @@map(\"GenerationSessions\")\n}\n\nmodel User {\n  userID       String    @id @default(uuid()) @db.Uuid\n  username     String    @unique @db.VarChar(100)\n  email        String    @unique @db.VarChar(255)\n  passwordHash String    @db.VarChar(255)\n  role         UserRole  @default(INSTRUCTOR)\n  departmentID String?   @db.Uuid\n  instructorID String?   @db.Uuid\n  isActive     Boolean   @default(true)\n  lastLogin    DateTime?\n  createdAt    DateTime  @default(now()) @map(\"created_at\")\n  updatedAt    DateTime  @updatedAt @map(\"updated_at\")\n\n  // Relations\n  department Department? @relation(fields: [departmentID], references: [departmentID])\n  instructor Instructor? @relation(fields: [instructorID], references: [instructorID])\n\n  @@map(\"Users\")\n}\n",
  "inlineSchemaHash": "5542e11b6e9adf30ed6e8915dcffd8e6d1c10f832948e6544c2195218eaef84c",
  "copyEngine": true
}

const fs = require('fs')

config.dirname = __dirname
if (!fs.existsSync(path.join(__dirname, 'schema.prisma'))) {
  const alternativePaths = [
    "node_modules/.prisma/client",
    ".prisma/client",
  ]
  
  const alternativePath = alternativePaths.find((altPath) => {
    return fs.existsSync(path.join(process.cwd(), altPath, 'schema.prisma'))
  }) ?? alternativePaths[0]

  config.dirname = path.join(process.cwd(), alternativePath)
  config.isBundled = true
}

config.runtimeDataModel = JSON.parse("{\"models\":{\"Semester\":{\"dbName\":\"Semesters\",\"fields\":[{\"name\":\"semesterID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"startDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"endDate\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":false,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"lessons\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Lesson\",\"relationName\":\"LessonToSemester\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scheduledLessons\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ScheduledLesson\",\"relationName\":\"ScheduledLessonToSemester\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publishedSchedules\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"PublishedSchedule\",\"relationName\":\"PublishedScheduleToSemester\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"generationSessions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"GenerationSession\",\"relationName\":\"GenerationSessionToSemester\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"constraints\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"SchedulingConstraint\",\"relationName\":\"SchedulingConstraintToSemester\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Department\":{\"dbName\":\"Departments\",\"fields\":[{\"name\":\"departmentID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"code\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"instructors\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Instructor\",\"relationName\":\"DepartmentToInstructor\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"rooms\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Room\",\"relationName\":\"DepartmentToRoom\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"studentLevels\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"StudentLevel\",\"relationName\":\"DepartmentToStudentLevel\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"courses\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Course\",\"relationName\":\"CourseToDepartment\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"users\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"relationName\":\"DepartmentToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Room\":{\"dbName\":\"Rooms\",\"fields\":[{\"name\":\"roomID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"roomNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"maxCapacity\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"type\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"RoomType\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"assignedDepartmentID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"building\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"floorNumber\",\"dbName\":\"floor_number\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"equipmentNotes\",\"dbName\":\"equipment_notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"assignedDepartment\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Department\",\"relationName\":\"DepartmentToRoom\",\"relationFromFields\":[\"assignedDepartmentID\"],\"relationToFields\":[\"departmentID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scheduledLessons\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ScheduledLesson\",\"relationName\":\"RoomToScheduledLesson\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"Instructor\":{\"dbName\":\"Instructors\",\"fields\":[{\"name\":\"instructorID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"departmentID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"maxDailySlots\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":20,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"department\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Department\",\"relationName\":\"DepartmentToInstructor\",\"relationFromFields\":[\"departmentID\"],\"relationToFields\":[\"departmentID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"availability\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"InstructorAvailability\",\"relationName\":\"InstructorToInstructorAvailability\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lessons\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Lesson\",\"relationName\":\"InstructorToLesson\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"originalSubstitutions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Substitution\",\"relationName\":\"OriginalInstructor\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"replacementSubstitutions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Substitution\",\"relationName\":\"ReplacementInstructor\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"users\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"User\",\"relationName\":\"InstructorToUser\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"InstructorAvailability\":{\"dbName\":\"InstructorAvailability\",\"fields\":[{\"name\":\"availabilityID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"instructorID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"dayOfWeek\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"availableSlots\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Json\",\"default\":\"[]\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"instructor\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Instructor\",\"relationName\":\"InstructorToInstructorAvailability\",\"relationFromFields\":[\"instructorID\"],\"relationToFields\":[\"instructorID\"],\"relationOnDelete\":\"Cascade\",\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"instructorID\",\"dayOfWeek\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"instructorID\",\"dayOfWeek\"]}],\"isGenerated\":false},\"StudentLevel\":{\"dbName\":\"StudentLevels\",\"fields\":[{\"name\":\"levelID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"departmentID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"expectedStudentCount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"yearNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semesterNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"department\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Department\",\"relationName\":\"DepartmentToStudentLevel\",\"relationFromFields\":[\"departmentID\"],\"relationToFields\":[\"departmentID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"studentGroups\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"StudentGroup\",\"relationName\":\"StudentGroupToStudentLevel\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"StudentGroup\":{\"dbName\":\"StudentGroups\",\"fields\":[{\"name\":\"groupID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"levelID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"groupName\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"parentGroupID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"groupType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"GroupType\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"studentCount\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"level\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"StudentLevel\",\"relationName\":\"StudentGroupToStudentLevel\",\"relationFromFields\":[\"levelID\"],\"relationToFields\":[\"levelID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"parentGroup\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"StudentGroup\",\"relationName\":\"GroupHierarchy\",\"relationFromFields\":[\"parentGroupID\"],\"relationToFields\":[\"groupID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"subGroups\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"StudentGroup\",\"relationName\":\"GroupHierarchy\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lessons\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Lesson\",\"relationName\":\"LessonToStudentGroup\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"levelID\",\"groupName\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"levelID\",\"groupName\"]}],\"isGenerated\":false},\"Course\":{\"dbName\":\"Courses\",\"fields\":[{\"name\":\"courseID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"courseCode\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"title\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"departmentID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"credits\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"department\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Department\",\"relationName\":\"CourseToDepartment\",\"relationFromFields\":[\"departmentID\"],\"relationToFields\":[\"departmentID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lessons\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Lesson\",\"relationName\":\"CourseToLesson\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"courseCode\",\"departmentID\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"courseCode\",\"departmentID\"]}],\"isGenerated\":false},\"Lesson\":{\"dbName\":\"Lessons\",\"fields\":[{\"name\":\"lessonID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"courseID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"studentGroupID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"instructorID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"durationInSlots\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lessonType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"LessonType\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"requiredRoomType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"RoomType\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semesterID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"sessionsPerWeek\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalSessions\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"priority\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":5,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"specialRequirements\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"course\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Course\",\"relationName\":\"CourseToLesson\",\"relationFromFields\":[\"courseID\"],\"relationToFields\":[\"courseID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"studentGroup\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"StudentGroup\",\"relationName\":\"LessonToStudentGroup\",\"relationFromFields\":[\"studentGroupID\"],\"relationToFields\":[\"groupID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"instructor\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Instructor\",\"relationName\":\"InstructorToLesson\",\"relationFromFields\":[\"instructorID\"],\"relationToFields\":[\"instructorID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semester\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Semester\",\"relationName\":\"LessonToSemester\",\"relationFromFields\":[\"semesterID\"],\"relationToFields\":[\"semesterID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scheduledLessons\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ScheduledLesson\",\"relationName\":\"LessonToScheduledLesson\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"ScheduledLesson\":{\"dbName\":\"ScheduledLessons\",\"fields\":[{\"name\":\"scheduledLessonID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lessonID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"roomID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"dayOfWeek\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DayOfWeek\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"startSlot\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"endSlot\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semesterID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"weekNumber\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"ScheduleStatus\",\"default\":\"SCHEDULED\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lockedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lockedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"lesson\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Lesson\",\"relationName\":\"LessonToScheduledLesson\",\"relationFromFields\":[\"lessonID\"],\"relationToFields\":[\"lessonID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"room\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Room\",\"relationName\":\"RoomToScheduledLesson\",\"relationFromFields\":[\"roomID\"],\"relationToFields\":[\"roomID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semester\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Semester\",\"relationName\":\"ScheduledLessonToSemester\",\"relationFromFields\":[\"semesterID\"],\"relationToFields\":[\"semesterID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"substitutions\",\"kind\":\"object\",\"isList\":true,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Substitution\",\"relationName\":\"ScheduledLessonToSubstitution\",\"relationFromFields\":[],\"relationToFields\":[],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[[\"roomID\",\"dayOfWeek\",\"startSlot\",\"endSlot\",\"semesterID\",\"weekNumber\"],[\"lessonID\",\"dayOfWeek\",\"startSlot\",\"endSlot\",\"semesterID\",\"weekNumber\"]],\"uniqueIndexes\":[{\"name\":null,\"fields\":[\"roomID\",\"dayOfWeek\",\"startSlot\",\"endSlot\",\"semesterID\",\"weekNumber\"]},{\"name\":null,\"fields\":[\"lessonID\",\"dayOfWeek\",\"startSlot\",\"endSlot\",\"semesterID\",\"weekNumber\"]}],\"isGenerated\":false},\"Substitution\":{\"dbName\":\"Substitutions\",\"fields\":[{\"name\":\"substitutionID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"originalInstructorID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"replacementInstructorID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scheduledLessonID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"dateOfAbsence\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"reason\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"SubstitutionStatus\",\"default\":\"PENDING\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"requestedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"approvedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"originalInstructor\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Instructor\",\"relationName\":\"OriginalInstructor\",\"relationFromFields\":[\"originalInstructorID\"],\"relationToFields\":[\"instructorID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"replacementInstructor\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Instructor\",\"relationName\":\"ReplacementInstructor\",\"relationFromFields\":[\"replacementInstructorID\"],\"relationToFields\":[\"instructorID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scheduledLesson\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ScheduledLesson\",\"relationName\":\"ScheduledLessonToSubstitution\",\"relationFromFields\":[\"scheduledLessonID\"],\"relationToFields\":[\"scheduledLessonID\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"PublishedSchedule\":{\"dbName\":\"PublishedSchedules\",\"fields\":[{\"name\":\"publishedScheduleID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semesterID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scheduleData\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publishedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"publishedBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"version\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"notes\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semester\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Semester\",\"relationName\":\"PublishedScheduleToSemester\",\"relationFromFields\":[\"semesterID\"],\"relationToFields\":[\"semesterID\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"SchedulingConstraint\":{\"dbName\":\"SchedulingConstraints\",\"fields\":[{\"name\":\"constraintID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"name\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"description\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"constraintType\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"ConstraintType\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"ruleDefinition\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"weight\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Int\",\"default\":1,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semesterID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"semester\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Semester\",\"relationName\":\"SchedulingConstraintToSemester\",\"relationFromFields\":[\"semesterID\"],\"relationToFields\":[\"semesterID\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"GenerationSession\":{\"dbName\":\"GenerationSessions\",\"fields\":[{\"name\":\"sessionID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"semesterID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"status\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"GenerationStatus\",\"default\":\"NOT_STARTED\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"manifestFilePath\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"jobID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"generationStartedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"generationCompletedAt\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"totalLessons\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"scheduledLessons\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"unscheduledLessons\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Int\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"qualityScore\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Decimal\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"constraintViolations\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Json\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"errorLog\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdBy\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"semester\",\"kind\":\"object\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Semester\",\"relationName\":\"GenerationSessionToSemester\",\"relationFromFields\":[\"semesterID\"],\"relationToFields\":[\"semesterID\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false},\"User\":{\"dbName\":\"Users\",\"fields\":[{\"name\":\"userID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":true,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"String\",\"default\":{\"name\":\"uuid(4)\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"username\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"email\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":true,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"passwordHash\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"role\",\"kind\":\"enum\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"UserRole\",\"default\":\"INSTRUCTOR\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"departmentID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"instructorID\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":true,\"hasDefaultValue\":false,\"type\":\"String\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"isActive\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"Boolean\",\"default\":true,\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"lastLogin\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"createdAt\",\"dbName\":\"created_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":true,\"type\":\"DateTime\",\"default\":{\"name\":\"now\",\"args\":[]},\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"updatedAt\",\"dbName\":\"updated_at\",\"kind\":\"scalar\",\"isList\":false,\"isRequired\":true,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"DateTime\",\"isGenerated\":false,\"isUpdatedAt\":true},{\"name\":\"department\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Department\",\"relationName\":\"DepartmentToUser\",\"relationFromFields\":[\"departmentID\"],\"relationToFields\":[\"departmentID\"],\"isGenerated\":false,\"isUpdatedAt\":false},{\"name\":\"instructor\",\"kind\":\"object\",\"isList\":false,\"isRequired\":false,\"isUnique\":false,\"isId\":false,\"isReadOnly\":false,\"hasDefaultValue\":false,\"type\":\"Instructor\",\"relationName\":\"InstructorToUser\",\"relationFromFields\":[\"instructorID\"],\"relationToFields\":[\"instructorID\"],\"isGenerated\":false,\"isUpdatedAt\":false}],\"primaryKey\":null,\"uniqueFields\":[],\"uniqueIndexes\":[],\"isGenerated\":false}},\"enums\":{\"RoomType\":{\"values\":[{\"name\":\"LECTURE_HALL\",\"dbName\":\"Lecture Hall\"},{\"name\":\"CLASSROOM\",\"dbName\":\"Classroom\"},{\"name\":\"LAB\",\"dbName\":\"Lab\"},{\"name\":\"TUTORIAL_ROOM\",\"dbName\":\"Tutorial Room\"},{\"name\":\"COMPUTER_LAB\",\"dbName\":\"Computer Lab\"}],\"dbName\":\"room_type_enum\"},\"GroupType\":{\"values\":[{\"name\":\"FULL_LEVEL\",\"dbName\":\"Full Level\"},{\"name\":\"GROUP\",\"dbName\":\"Group\"},{\"name\":\"SUB_GROUP\",\"dbName\":\"Sub-Group\"}],\"dbName\":\"group_type_enum\"},\"LessonType\":{\"values\":[{\"name\":\"LECTURE\",\"dbName\":\"Lecture\"},{\"name\":\"LAB\",\"dbName\":\"Lab\"},{\"name\":\"TUTORIAL\",\"dbName\":\"Tutorial\"},{\"name\":\"COMPUTER_LAB\",\"dbName\":\"Computer Lab\"},{\"name\":\"SEMINAR\",\"dbName\":\"Seminar\"},{\"name\":\"WORKSHOP\",\"dbName\":\"Workshop\"}],\"dbName\":\"lesson_type_enum\"},\"ScheduleStatus\":{\"values\":[{\"name\":\"SCHEDULED\",\"dbName\":\"Scheduled\"},{\"name\":\"LOCKED\",\"dbName\":\"Locked\"},{\"name\":\"CANCELLED\",\"dbName\":\"Cancelled\"},{\"name\":\"MODIFIED\",\"dbName\":\"Modified\"}],\"dbName\":\"schedule_status_enum\"},\"DayOfWeek\":{\"values\":[{\"name\":\"MONDAY\",\"dbName\":\"Monday\"},{\"name\":\"TUESDAY\",\"dbName\":\"Tuesday\"},{\"name\":\"WEDNESDAY\",\"dbName\":\"Wednesday\"},{\"name\":\"THURSDAY\",\"dbName\":\"Thursday\"},{\"name\":\"FRIDAY\",\"dbName\":\"Friday\"},{\"name\":\"SATURDAY\",\"dbName\":\"Saturday\"},{\"name\":\"SUNDAY\",\"dbName\":\"Sunday\"}],\"dbName\":\"day_of_week_enum\"},\"SubstitutionStatus\":{\"values\":[{\"name\":\"PENDING\",\"dbName\":\"Pending\"},{\"name\":\"APPROVED\",\"dbName\":\"Approved\"},{\"name\":\"REJECTED\",\"dbName\":\"Rejected\"},{\"name\":\"COMPLETED\",\"dbName\":\"Completed\"}],\"dbName\":\"substitution_status_enum\"},\"ConstraintType\":{\"values\":[{\"name\":\"HARD\",\"dbName\":\"Hard\"},{\"name\":\"SOFT\",\"dbName\":\"Soft\"},{\"name\":\"ADVANCED\",\"dbName\":\"Advanced\"}],\"dbName\":\"constraint_type_enum\"},\"GenerationStatus\":{\"values\":[{\"name\":\"NOT_STARTED\",\"dbName\":\"NOT_STARTED\"},{\"name\":\"DATA_INPUT\",\"dbName\":\"DATA_INPUT\"},{\"name\":\"MANIFEST_GENERATED\",\"dbName\":\"MANIFEST_GENERATED\"},{\"name\":\"MANIFEST_VALIDATED\",\"dbName\":\"MANIFEST_VALIDATED\"},{\"name\":\"SCHEDULING_IN_PROGRESS\",\"dbName\":\"SCHEDULING_IN_PROGRESS\"},{\"name\":\"SCHEDULING_COMPLETED\",\"dbName\":\"SCHEDULING_COMPLETED\"},{\"name\":\"SCHEDULING_FAILED\",\"dbName\":\"SCHEDULING_FAILED\"},{\"name\":\"PUBLISHED\",\"dbName\":\"PUBLISHED\"}],\"dbName\":\"generation_status_enum\"},\"UserRole\":{\"values\":[{\"name\":\"ADMIN\",\"dbName\":\"admin\"},{\"name\":\"SCHEDULER\",\"dbName\":\"scheduler\"},{\"name\":\"DEPARTMENT_COORDINATOR\",\"dbName\":\"department_coordinator\"},{\"name\":\"INSTRUCTOR\",\"dbName\":\"instructor\"},{\"name\":\"STUDENT\",\"dbName\":\"student\"}],\"dbName\":\"user_role_enum\"}},\"types\":{}}")
defineDmmfProperty(exports.Prisma, config.runtimeDataModel)
config.engineWasm = undefined


const { warnEnvConflicts } = require('@prisma/client/runtime/library.js')

warnEnvConflicts({
    rootEnvPath: config.relativeEnvPaths.rootEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.rootEnvPath),
    schemaEnvPath: config.relativeEnvPaths.schemaEnvPath && path.resolve(config.dirname, config.relativeEnvPaths.schemaEnvPath)
})

const PrismaClient = getPrismaClient(config)
exports.PrismaClient = PrismaClient
Object.assign(exports, Prisma)

// file annotations for bundling tools to include these files
path.join(__dirname, "query_engine-windows.dll.node");
path.join(process.cwd(), "node_modules/.prisma/client/query_engine-windows.dll.node")
// file annotations for bundling tools to include these files
path.join(__dirname, "schema.prisma");
path.join(process.cwd(), "node_modules/.prisma/client/schema.prisma")
