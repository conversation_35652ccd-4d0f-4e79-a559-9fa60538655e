import { useState } from "react";
import { Plus, Calendar, Edit, Trash2, CheckCircle, XCircle } from "lucide-react";
import { Button } from "~/components/ui/button";

// Mock data for demonstration
const mockSemesters = [
  {
    semesterID: "1",
    name: "Fall 2024",
    startDate: "2024-08-26",
    endDate: "2024-12-15",
    isActive: true,
    createdAt: "2024-01-15",
  },
  {
    semesterID: "2",
    name: "Spring 2024",
    startDate: "2024-01-15",
    endDate: "2024-05-10",
    isActive: false,
    createdAt: "2023-10-15",
  },
  {
    semesterID: "3",
    name: "Summer 2024",
    startDate: "2024-05-20",
    endDate: "2024-08-15",
    isActive: false,
    createdAt: "2024-02-15",
  },
];

export default function Semesters() {
  const [semesters, setSemesters] = useState(mockSemesters);
  const [showCreateForm, setShowCreateForm] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleActivateSemester = (semesterID: string) => {
    setSemesters(prev => prev.map(semester => ({
      ...semester,
      isActive: semester.semesterID === semesterID
    })));
  };

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Semesters</h2>
          <p className="text-muted-foreground">
            Manage academic semesters and their schedules
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Semester
        </Button>
      </div>

      {/* Semesters Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {semesters.map((semester) => (
          <div
            key={semester.semesterID}
            className={`rounded-lg border bg-card text-card-foreground shadow-sm p-6 ${
              semester.isActive ? 'ring-2 ring-primary' : ''
            }`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Calendar className="h-5 w-5 text-primary" />
                <h3 className="text-lg font-semibold">{semester.name}</h3>
              </div>
              {semester.isActive && (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Active</span>
                </div>
              )}
            </div>

            <div className="space-y-2 mb-4">
              <div className="text-sm">
                <span className="font-medium">Start:</span> {formatDate(semester.startDate)}
              </div>
              <div className="text-sm">
                <span className="font-medium">End:</span> {formatDate(semester.endDate)}
              </div>
              <div className="text-sm text-muted-foreground">
                Created: {formatDate(semester.createdAt)}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                <Button size="sm" variant="outline">
                  <Edit className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline">
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
              {!semester.isActive && (
                <Button
                  size="sm"
                  onClick={() => handleActivateSemester(semester.semesterID)}
                >
                  Activate
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Create Form Modal (simplified for demo) */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Create New Semester</h3>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Semester Name</label>
                <input
                  type="text"
                  className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  placeholder="e.g., Fall 2024"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Start Date</label>
                <input
                  type="date"
                  className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                />
              </div>
              <div>
                <label className="text-sm font-medium">End Date</label>
                <input
                  type="date"
                  className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                />
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="isActive" />
                <label htmlFor="isActive" className="text-sm">Set as active semester</label>
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowCreateForm(false)}>
                Create Semester
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-3">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
          <div className="text-2xl font-bold text-center">{semesters.length}</div>
          <div className="text-sm text-muted-foreground text-center">Total Semesters</div>
        </div>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
          <div className="text-2xl font-bold text-center text-green-600">
            {semesters.filter(s => s.isActive).length}
          </div>
          <div className="text-sm text-muted-foreground text-center">Active Semester</div>
        </div>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
          <div className="text-2xl font-bold text-center">
            {semesters.filter(s => new Date(s.endDate) > new Date()).length}
          </div>
          <div className="text-sm text-muted-foreground text-center">Upcoming Semesters</div>
        </div>
      </div>
    </div>
  );
}