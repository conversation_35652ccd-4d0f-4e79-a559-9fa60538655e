@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Status Check
:: This script checks the status of all application services

echo ========================================
echo University Schedule Assistant - Status
echo ========================================
echo.

:: Set color for better visibility
color 0B

:: Configuration
set "BACKEND_PORT=3001"
set "FRONTEND_PORT=3000"
set "DB_PORT=5432"
set "REDIS_PORT=6379"

echo [INFO] Checking University Schedule Assistant status...
echo.

:: Check Docker status
echo [INFO] Docker Status:
echo ========================================
docker info >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Docker is running
) else (
    echo [ERROR] Docker is not running
)
echo.

:: Check container status
echo [INFO] Container Status:
echo ========================================
docker-compose ps
echo.

:: Check service health
echo [INFO] Service Health Checks:
echo ========================================

:: Backend health check
curl -f http://localhost:%BACKEND_PORT%/health >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Backend is healthy (http://localhost:%BACKEND_PORT%)
) else (
    echo [ERROR] Backend is not responding (http://localhost:%BACKEND_PORT%)
)

:: Frontend health check
curl -f http://localhost:%FRONTEND_PORT% >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Frontend is healthy (http://localhost:%FRONTEND_PORT%)
) else (
    echo [ERROR] Frontend is not responding (http://localhost:%FRONTEND_PORT%)
)

:: Database connection check
docker-compose exec -T postgres pg_isready -U postgres >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Database is healthy
) else (
    echo [ERROR] Database is not responding
)

:: Redis connection check
docker-compose exec -T redis redis-cli ping >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Redis is healthy
) else (
    echo [ERROR] Redis is not responding
)
echo.

:: Show resource usage
echo [INFO] Resource Usage:
echo ========================================
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
echo.

:: Show recent logs
echo [INFO] Recent Logs (last 10 lines):
echo ========================================
echo [Backend Logs:]
docker-compose logs --tail=5 backend
echo.
echo [Frontend Logs:]
docker-compose logs --tail=5 frontend
echo.

:: Application URLs
echo [INFO] Application URLs:
echo ========================================
echo Frontend:     http://localhost:%FRONTEND_PORT%
echo Backend API:  http://localhost:%BACKEND_PORT%
echo Health Check: http://localhost:%BACKEND_PORT%/health
echo API Docs:     http://localhost:%BACKEND_PORT%/api-docs (if available)
echo ========================================
echo.

:: Management commands
echo [INFO] Management Commands:
echo ========================================
echo View logs:           docker-compose logs -f [service_name]
echo Restart service:     docker-compose restart [service_name]
echo Stop application:    stop-application.bat
echo Start application:   start-application.bat
echo ========================================
echo.

echo [INFO] Status check completed!
pause