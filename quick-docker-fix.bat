@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Quick Docker Fix Script
:: This script provides a quick fix for Docker storage issues

echo ========================================
echo Quick Docker Fix - University Schedule Assistant
echo ========================================
echo.

:: Set color for better visibility
color 0E

echo [INFO] Quick Docker storage fix
echo [INFO] This script will perform a quick Docker reset
echo.

echo [INFO] Options available:
echo 1. Quick system prune (removes unused containers, networks, images)
echo 2. Full Docker reset (factory reset - removes everything)
echo 3. Manual troubleshooting steps
echo.

set /p "choice=Choose option (1/2/3): "

if "!choice!"=="1" goto quick_prune
if "!choice!"=="2" goto full_reset
if "!choice!"=="3" goto manual_steps

echo [ERROR] Invalid choice
pause
exit /b 1

:quick_prune
echo.
echo [INFO] Performing quick system prune...
echo [INFO] This will remove unused containers, networks, and images

docker system prune -a -f --volumes
if %errorLevel% equ 0 (
    echo [SUCCESS] Quick prune completed
    echo [INFO] Try running start-application.bat now
) else (
    echo [ERROR] Quick prune failed
    echo [INFO] Try option 2 (Full Docker reset)
)
goto end

:full_reset
echo.
echo [WARNING] This will perform a factory reset of Docker Desktop
echo [WARNING] All containers, images, and volumes will be removed
echo.

set /p "confirm=Are you sure? (Y/N): "
if /i "!confirm!" neq "Y" (
    echo [INFO] Operation cancelled
    goto end
)

echo [INFO] Performing Docker Desktop factory reset...
echo [INFO] Please follow these steps:

echo.
echo [MANUAL STEPS REQUIRED]:
echo 1. Open Docker Desktop
echo 2. Click on Settings (gear icon)
echo 3. Go to "Troubleshoot" tab
echo 4. Click "Reset to factory defaults"
echo 5. Click "Reset" to confirm
echo 6. Wait for Docker to restart
echo 7. Run start-application.bat after reset completes
echo.

echo [INFO] Opening Docker Desktop for you...
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
goto end

:manual_steps
echo.
echo [INFO] Manual troubleshooting steps:
echo.
echo [STEP 1] Try restarting Docker Desktop:
echo - Right-click Docker Desktop system tray icon
echo - Select "Restart Docker Desktop"
echo.
echo [STEP 2] If restart doesn't work, try WSL reset:
echo - Open PowerShell as Administrator
echo - Run: wsl --shutdown
echo - Wait 10 seconds
echo - Start Docker Desktop again
echo.
echo [STEP 3] If still having issues, check disk space:
echo - Ensure you have at least 10GB free space
echo - Docker needs space for images and containers
echo.
echo [STEP 4] Check Windows updates:
echo - Ensure Windows is up to date
echo - Restart computer if updates were installed
echo.
echo [STEP 5] If all else fails:
echo - Run fix-docker-storage.bat for complete cleanup
echo - Or use option 2 for factory reset
echo.

:end
echo.
echo [INFO] Docker fix options completed
echo [INFO] If issues persist, try the other options or contact support
pause