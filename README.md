# University Schedule Assistant

A comprehensive, intelligent academic scheduling system designed to automate and optimize university timetable generation with capabilities comparable to leading commercial software like aSc TimeTables.

## 🎯 Project Overview

The University Schedule Assistant is a modern web-based application that transforms the complex process of academic scheduling into an efficient, automated workflow. The system ingests university operational data and constraints to generate conflict-free, optimized course schedules while providing intuitive tools for manual adjustments and daily operational management.

### Key Features

- **Intelligent Scheduling Engine**: Advanced constraint satisfaction algorithms with optimization
- **Excel Integration**: Seamless import/export with manifest-based workflow
- **Real-time Conflict Detection**: Immediate validation and resolution suggestions
- **Substitution Management**: Automated daily substitution handling with approval workflows
- **Multi-format Output**: PDF, Excel, iCal, and web-based schedule distribution
- **Role-based Access Control**: Granular permissions for different user types
- **Analytics Dashboard**: Comprehensive reporting and performance metrics
- **Mobile-friendly Interface**: Responsive design for all devices

## 📋 Documentation

This repository contains the complete design documentation for the University Schedule Assistant:

### Core Design Documents

1. **[Database Schema](./database_schema.sql)** - Comprehensive PostgreSQL schema with all entities, relationships, and constraints
2. **[REST API Design](./api_design.md)** - Complete API specification with 100+ endpoints covering all functionality
3. **[System Architecture](./system_architecture.md)** - Technical architecture, technology stack, and deployment strategy
4. **[Workflow Process](./workflow_process.md)** - Detailed user workflow from data input to daily operations

## 🏗️ System Architecture

### Technology Stack

**Backend**
- Node.js 18+ with TypeScript
- Express.js framework
- PostgreSQL 15+ (primary database)
- Redis (caching & sessions)
- Prisma ORM
- JWT authentication
- Socket.IO for real-time updates

**Frontend**
- React 18 with TypeScript
- Vite build tool
- Material-UI (MUI) v5
- TanStack Query for data fetching
- Zustand for state management
- FullCalendar for schedule visualization

**Infrastructure**
- Docker containerization
- Nginx reverse proxy
- AWS cloud deployment
- Prometheus + Grafana monitoring

### Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                          CLIENT LAYER                           │
├─────────────────────────────────────────────────────────────────┤
│  React Frontend (Vite)  │  Mobile App (Future)  │  Excel Files  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                        API GATEWAY                              │
├─────────────────────────────────────────────────────────────────┤
│              Nginx (Load Balancer, SSL, Rate Limiting)          │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      APPLICATION LAYER                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Auth      │  │   Core      │  │ Scheduling  │  │ Reports │ │
│  │  Service    │  │  Service    │  │   Engine    │  │ Service │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │Substitution │  │   Excel     │  │ Notification│  │WebSocket│ │
│  │  Service    │  │  Service    │  │   Service   │  │ Service │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                       DATA LAYER                                │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ PostgreSQL  │  │    Redis    │  │   File      │  │  Queue  │ │
│  │ (Primary)   │  │  (Cache)    │  │  Storage    │  │ System  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 Workflow Overview

The system follows a structured workflow designed to ensure data integrity and provide validation checkpoints:

### 1. Data Input Phase
- **Manual Entry**: Web-based forms for individual data entry
- **Bulk Import**: Excel template-based bulk data upload
- **Validation**: Real-time data validation and quality checks

### 2. Manifest Generation
- **Comprehensive Review**: Generate Excel manifest of all scheduling requirements
- **Offline Editing**: Allow modifications to the manifest before generation
- **Validation**: Ensure data integrity before proceeding

### 3. Automated Scheduling
- **Constraint Satisfaction**: Apply hard and soft constraints
- **Optimization**: Minimize gaps, maximize resource utilization
- **Progress Monitoring**: Real-time generation progress tracking

### 4. Manual Refinement
- **Interactive Tools**: Drag-and-drop schedule adjustments
- **Conflict Resolution**: Automatic suggestions and manual overrides
- **Quality Optimization**: Re-run optimization with locked manual changes

### 5. Publication & Operations
- **Multi-format Distribution**: PDF, Excel, web, and mobile access
- **Daily Management**: Substitution handling and bulletin generation
- **Analytics**: Performance monitoring and improvement insights

## 📊 Core Data Models

### Primary Entities

- **Semesters**: Academic periods with start/end dates
- **Departments**: Organizational units with assigned instructors
- **Rooms**: Physical spaces with capacity and type constraints
- **Instructors**: Faculty with availability and preferences
- **Student Levels**: Academic levels (e.g., "First Year Pharmacy")
- **Student Groups**: Hierarchical groupings within levels
- **Courses**: Abstract course definitions
- **Lessons**: Concrete scheduling instances requiring placement

### Time Structure

The system operates on a 26-slot time grid representing a working day from 9:30 AM to 4:00 PM, with each slot representing 15 minutes. This granular approach allows for flexible scheduling while maintaining consistency.

## 🎛️ Key Features

### Intelligent Scheduling Engine

- **Constraint Satisfaction**: Handles complex hard and soft constraints
- **Optimization Algorithms**: Minimizes gaps and maximizes resource utilization
- **Conflict Detection**: Real-time validation with resolution suggestions
- **Quality Scoring**: Comprehensive metrics for schedule quality assessment

### Excel Integration

- **Template-based Import**: Structured Excel templates for bulk data entry
- **Manifest Generation**: Comprehensive scheduling requirements document
- **Offline Editing**: Modify scheduling requirements before generation
- **Multi-format Export**: Various output formats for different stakeholders

### Substitution Management

- **Automated Suggestions**: Find qualified available substitutes
- **Approval Workflows**: Department-based approval processes
- **Daily Bulletins**: Automated generation and distribution
- **Emergency Procedures**: Fast-track processes for urgent situations

### Real-time Features

- **Live Updates**: WebSocket-based real-time schedule updates
- **Progress Tracking**: Live generation progress monitoring
- **Collaborative Editing**: Multiple users can work simultaneously
- **Instant Notifications**: Real-time alerts for important events

## 🚀 Implementation Roadmap

### Phase 1: Foundation (Months 1-3)
- Database setup and core data models
- Basic CRUD operations and user management
- Excel import/export functionality
- Authentication and authorization system

### Phase 2: Core Scheduling (Months 4-6)
- Scheduling engine development
- Constraint management system
- Manual adjustment tools
- Conflict detection and resolution

### Phase 3: Advanced Features (Months 7-9)
- Substitution management system
- Real-time updates and notifications
- Reporting and analytics dashboard
- Mobile-responsive interface

### Phase 4: Polish & Launch (Months 10-12)
- User interface refinement
- Performance optimization
- Comprehensive testing
- User training and documentation
- Production deployment

## 📈 Success Metrics

### Operational Excellence
- **Schedule Generation Success Rate**: >95%
- **Generation Time**: <30 minutes for 500+ lessons
- **System Uptime**: >99.5%
- **Conflict Resolution Time**: <24 hours average

### Quality Assurance
- **Schedule Quality Score**: >85/100
- **User Satisfaction**: >4.0/5.0 (instructors and students)
- **Room Utilization**: 70-85% optimal range
- **Manual Adjustment Rate**: <10% of total lessons

### Efficiency Gains
- **Data Entry Time Reduction**: >50% vs. manual methods
- **Substitution Success Rate**: >90%
- **Report Generation Time**: <5 minutes for standard reports
- **Process Automation**: >80% of routine tasks automated

## 🔧 Getting Started

### Prerequisites

- Node.js 18+ and npm
- PostgreSQL 15+
- Redis 6+
- Docker (optional, for containerized deployment)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/university/schedule-assistant.git
   cd schedule-assistant
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database and configuration settings
   ```

4. **Initialize the database**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Access the application**
   - Frontend: http://localhost:3000
   - API: http://localhost:3001
   - API Documentation: http://localhost:3001/docs

### Docker Deployment

```bash
docker-compose up -d
```

## 🤝 Contributing

We welcome contributions to the University Schedule Assistant! Please read our [Contributing Guidelines](CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

### Development Workflow

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Standards

- TypeScript for all new code
- ESLint and Prettier for code formatting
- Jest for unit testing
- Comprehensive API documentation
- 80%+ test coverage requirement

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation

- [User Manual](docs/user-manual.md)
- [Administrator Guide](docs/admin-guide.md)
- [API Reference](docs/api-reference.md)
- [Deployment Guide](docs/deployment.md)

### Getting Help

- **Issues**: Report bugs and request features via [GitHub Issues](https://github.com/university/schedule-assistant/issues)
- **Discussions**: Join community discussions in [GitHub Discussions](https://github.com/university/schedule-assistant/discussions)
- **Email**: Contact the development <NAME_EMAIL>

### Professional Support

For enterprise deployments and professional support, contact our team for:
- Custom implementation services
- Training and onboarding
- Priority support and maintenance
- Custom feature development

## 🏆 Acknowledgments

- Inspired by aSc TimeTables and other leading scheduling software
- Built with modern web technologies and best practices
- Designed for the academic community by the academic community

## 🔮 Future Enhancements

### Planned Features

- **Mobile Application**: Native iOS and Android apps
- **AI-Powered Optimization**: Machine learning for schedule quality improvement
- **Integration APIs**: Connect with existing university systems (SIS, LMS)
- **Advanced Analytics**: Predictive analytics and trend analysis
- **Multi-language Support**: Internationalization for global universities
- **Accessibility Improvements**: Enhanced accessibility features

### Research Areas

- **Quantum Computing**: Explore quantum algorithms for complex scheduling
- **Blockchain Integration**: Immutable audit trails for schedule changes
- **IoT Integration**: Smart classroom and resource management
- **Virtual Reality**: 3D campus visualization for schedule planning

---

**Built with ❤️ for the academic community**

*Transform your university scheduling from chaos to clarity with the University Schedule Assistant.*