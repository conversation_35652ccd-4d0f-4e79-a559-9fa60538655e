import { Semester } from '@prisma/client';
import prisma from '../config/database';
import { CreateSemesterDTO, UpdateSemesterDTO, SemesterFilters } from '../types/api';
import { logger } from '../config/logger';

/**
 * Semester Service - Handles all semester-related business logic
 */
export class SemesterService {
  /**
   * Get all semesters with optional filtering and pagination
   */
  static async getAllSemesters(filters: SemesterFilters) {
    const { page = 1, limit = 20, active } = filters;
    const skip = (page - 1) * limit;

    const where: any = {};
    if (active !== undefined) {
      where.isActive = active;
    }

    const [semesters, total] = await Promise.all([
      prisma.semester.findMany({
        where,
        skip,
        take: limit,
        orderBy: [
          { isActive: 'desc' },
          { startDate: 'desc' },
        ],
      }),
      prisma.semester.count({ where }),
    ]);

    logger.info('Retrieved semesters', {
      count: semesters.length,
      total,
      filters
    });

    return { semesters, total };
  }

  /**
   * Get semester by ID
   */
  static async getSemesterById(semesterID: string): Promise<Semester | null> {
    const semester = await prisma.semester.findUnique({
      where: { semesterID },
    });

    if (!semester) {
      logger.warn('Semester not found', { semesterID });
      return null;
    }

    return semester;
  }

  /**
   * Get active semester
   */
  static async getActiveSemester(): Promise<Semester | null> {
    const semester = await prisma.semester.findFirst({
      where: { isActive: true },
    });

    if (!semester) {
      logger.warn('No active semester found');
      return null;
    }

    return semester;
  }

  /**
   * Create new semester
   */
  static async createSemester(data: CreateSemesterDTO): Promise<Semester> {
    // If this semester should be active, deactivate all others first
    if (data.isActive) {
      await this.deactivateAllSemesters();
    }

    const semester = await prisma.semester.create({
      data: {
        name: data.name,
        startDate: new Date(data.startDate),
        endDate: new Date(data.endDate),
        isActive: data.isActive || false,
      },
    });

    logger.info('Created semester', {
      semesterID: semester.semesterID,
      name: semester.name
    });

    return semester;
  }

  /**
   * Update semester
   */
  static async updateSemester(
    semesterID: string,
    data: UpdateSemesterDTO
  ): Promise<Semester | null> {
    // Check if semester exists
    const existingSemester = await this.getSemesterById(semesterID);
    if (!existingSemester) {
      return null;
    }

    // If setting this semester as active, deactivate all others first
    if (data.isActive) {
      await this.deactivateAllSemesters();
    }

    const updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.startDate !== undefined) updateData.startDate = new Date(data.startDate);
    if (data.endDate !== undefined) updateData.endDate = new Date(data.endDate);
    if (data.isActive !== undefined) updateData.isActive = data.isActive;

    const semester = await prisma.semester.update({
      where: { semesterID },
      data: updateData,
    });

    logger.info('Updated semester', {
      semesterID,
      changes: Object.keys(updateData)
    });

    return semester;
  }

  /**
   * Delete semester
   */
  static async deleteSemester(semesterID: string): Promise<boolean> {
    try {
      // Check if semester has any associated data
      const [lessonsCount, scheduledLessonsCount, generationSessionsCount] = await Promise.all([
        prisma.lesson.count({ where: { semesterID } }),
        prisma.scheduledLesson.count({ where: { semesterID } }),
        prisma.generationSession.count({ where: { semesterID } }),
      ]);

      if (lessonsCount > 0 || scheduledLessonsCount > 0 || generationSessionsCount > 0) {
        logger.warn('Cannot delete semester with associated data', {
          semesterID,
          lessonsCount,
          scheduledLessonsCount,
          generationSessionsCount,
        });
        throw new Error('Cannot delete semester with associated lessons or schedules');
      }

      await prisma.semester.delete({
        where: { semesterID },
      });

      logger.info('Deleted semester', { semesterID });
      return true;
    } catch (error) {
      logger.error('Failed to delete semester', { semesterID, error });
      throw error;
    }
  }

  /**
   * Activate semester (deactivates all others)
   */
  static async activateSemester(semesterID: string): Promise<Semester | null> {
    const semester = await this.getSemesterById(semesterID);
    if (!semester) {
      return null;
    }

    // Deactivate all semesters first
    await this.deactivateAllSemesters();

    // Activate the specified semester
    const activatedSemester = await prisma.semester.update({
      where: { semesterID },
      data: { isActive: true },
    });

    logger.info('Activated semester', { semesterID, name: semester.name });
    return activatedSemester;
  }

  /**
   * Deactivate all semesters
   */
  static async deactivateAllSemesters(): Promise<void> {
    await prisma.semester.updateMany({
      where: { isActive: true },
      data: { isActive: false },
    });

    logger.info('Deactivated all semesters');
  }

  /**
   * Validate semester dates
   */
  static validateSemesterDates(startDate: string, endDate: string): string[] {
    const errors: string[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();

    if (isNaN(start.getTime())) {
      errors.push('Invalid start date format');
    }

    if (isNaN(end.getTime())) {
      errors.push('Invalid end date format');
    }

    if (start >= end) {
      errors.push('Start date must be before end date');
    }

    if (end <= now) {
      errors.push('End date must be in the future');
    }

    // Semester should be at least 1 month long
    const oneMonth = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
    if (end.getTime() - start.getTime() < oneMonth) {
      errors.push('Semester must be at least 30 days long');
    }

    return errors;
  }

  /**
   * Check if semester name is unique
   */
  static async isSemesterNameUnique(name: string, excludeId?: string): Promise<boolean> {
    const where: any = { name };
    if (excludeId) {
      where.semesterID = { not: excludeId };
    }

    const existingSemester = await prisma.semester.findFirst({ where });
    return !existingSemester;
  }
}