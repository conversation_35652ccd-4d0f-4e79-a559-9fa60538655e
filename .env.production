# Production Environment Variables
# Copy this file to .env and fill in the actual values

# Database Configuration
POSTGRES_DB=schedule_assistant_prod
POSTGRES_USER=schedule_user
POSTGRES_PASSWORD=your_secure_database_password_here

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password_here

# Application Security
JWT_SECRET=your_very_secure_jwt_secret_key_here_minimum_32_characters
SESSION_SECRET=your_secure_session_secret_here

# Application URLs
FRONTEND_URL=https://your-domain.com
BACKEND_URL=https://api.your-domain.com

# Email Configuration (for notifications)
SMTP_HOST=smtp.your-email-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_FROM=<EMAIL>

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=/app/logs/application.log

# Performance Configuration
NODE_ENV=production
PORT=3001

# Security Headers
HELMET_ENABLED=true
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# Monitoring
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30