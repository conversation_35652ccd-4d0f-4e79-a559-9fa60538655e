# University Schedule Assistant - Environment Configuration
# Copy this file to .env and update the values

# Server Configuration
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_URL=postgresql://schedule_user:schedule_password@localhost:5432/schedule_assistant?schema=public

# JWT Configuration (Generate secure random strings for production)
JWT_SECRET=your_very_secure_jwt_secret_key_here_minimum_32_characters_long
JWT_REFRESH_SECRET=your_very_secure_jwt_refresh_secret_key_here_minimum_32_characters_long
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760

# Email Configuration (Optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=

# Scheduling Engine Configuration
MAX_GENERATION_TIME=1800
DEFAULT_TIME_SLOTS=26
WORKING_DAY_START=09:30
WORKING_DAY_END=16:00

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your_secure_session_secret_here_minimum_32_characters_long

# Development/Testing
ENABLE_SWAGGER=true
ENABLE_PRISMA_STUDIO=true

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d

# Redis Configuration (for caching and job queue)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Upload Configuration
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB in bytes

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>

# Scheduling Engine Configuration
MAX_GENERATION_TIME=1800  # 30 minutes in seconds
DEFAULT_TIME_SLOTS=26
WORKING_DAY_START=09:30
WORKING_DAY_END=16:00

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this-in-production

# Development/Testing
ENABLE_SWAGGER=true
ENABLE_PRISMA_STUDIO=true