@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Windows Startup Script
:: This script prepares all dependencies, database, and starts the application

echo ========================================
echo University Schedule Assistant Startup
echo ========================================
echo.

:: Set color for better visibility
color 0A

:: Configuration
set "PROJECT_NAME=schedule-assistant"
set "BACKEND_PORT=3001"
set "FRONTEND_PORT=3000"
set "DB_PORT=5432"
set "REDIS_PORT=6379"

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [WARNING] Not running as administrator. Some operations may fail.
    echo [INFO] Consider running as administrator for full functionality.
    echo.
)

:: Function to check if a command exists
where docker >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker is not installed or not in PATH
    echo [INFO] Please install Docker Desktop from https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

where docker-compose >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker Compose is not installed or not in PATH
    echo [INFO] Please install Docker Compose
    pause
    exit /b 1
)

where node >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo [INFO] Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

where npm >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] npm is not installed or not in PATH
    echo [INFO] npm should come with Node.js installation
    pause
    exit /b 1
)

echo [INFO] All required tools are available
echo.

:: Check if .env file exists
if not exist ".env" (
    echo [INFO] Creating .env file from template...
    if exist ".env.production" (
        copy ".env.production" ".env" >nul
        echo [SUCCESS] .env file created from .env.production template
        echo [WARNING] Please edit .env file with your actual configuration values
        echo [INFO] Opening .env file for editing...
        notepad .env
        echo.
        echo [INFO] Press any key after editing .env file to continue...
        pause >nul
    ) else (
        echo [ERROR] No .env template found. Creating basic .env file...
        (
            echo # Database Configuration
            echo POSTGRES_DB=schedule_assistant
            echo POSTGRES_USER=postgres
            echo POSTGRES_PASSWORD=your_secure_password_here
            echo.
            echo # Redis Configuration
            echo REDIS_PASSWORD=your_redis_password_here
            echo.
            echo # Application Security
            echo JWT_SECRET=your_very_secure_jwt_secret_key_here_minimum_32_characters
            echo SESSION_SECRET=your_secure_session_secret_here
            echo.
            echo # Application URLs
            echo FRONTEND_URL=http://localhost:3000
            echo BACKEND_URL=http://localhost:3001
            echo.
            echo # Performance Configuration
            echo NODE_ENV=development
            echo PORT=3001
        ) > .env
        echo [SUCCESS] Basic .env file created
        echo [WARNING] Please edit .env file with your actual configuration values
        notepad .env
        echo.
        echo [INFO] Press any key after editing .env file to continue...
        pause >nul
    )
)

:: Create necessary directories
echo [INFO] Creating necessary directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups
if not exist "nginx\ssl" mkdir nginx\ssl
echo [SUCCESS] Directories created
echo.

:: Check Docker service status
echo [INFO] Checking Docker service status...
docker info >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Docker service is not running
    echo [INFO] Starting Docker Desktop...
    start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
    echo [INFO] Waiting for Docker to start... (this may take a few minutes)

    :wait_docker
    timeout /t 5 /nobreak >nul
    docker info >nul 2>&1
    if %errorLevel% neq 0 (
        echo [INFO] Still waiting for Docker...
        goto wait_docker
    )
    echo [SUCCESS] Docker is now running
) else (
    echo [SUCCESS] Docker is running
)
echo.

:: Stop any existing containers
echo [INFO] Stopping any existing containers...
docker-compose down >nul 2>&1
echo [SUCCESS] Existing containers stopped
echo.

:: Pull latest base images
echo [INFO] Pulling latest base images...
docker-compose pull
if %errorLevel% neq 0 (
    echo [WARNING] Failed to pull some images, continuing with local images
) else (
    echo [SUCCESS] Base images updated
)
echo.

:: Install backend dependencies
echo [INFO] Installing backend dependencies...
if exist "package.json" (
    npm install
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install backend dependencies
        pause
        exit /b 1
    )
    echo [SUCCESS] Backend dependencies installed
) else (
    echo [WARNING] No package.json found in root directory
)
echo.

:: Install frontend dependencies
echo [INFO] Installing frontend dependencies...
if exist "frontend\package.json" (
    cd frontend
    npm install
    if %errorLevel% neq 0 (
        echo [ERROR] Failed to install frontend dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [SUCCESS] Frontend dependencies installed
) else (
    echo [WARNING] No frontend package.json found
)
echo.

:: Build and start services
echo [INFO] Building and starting services...
echo [INFO] This may take several minutes on first run...
docker-compose up -d --build
if %errorLevel% neq 0 (
    echo [ERROR] Failed to start services
    echo [INFO] Checking logs...
    docker-compose logs
    pause
    exit /b 1
)
echo [SUCCESS] Services started
echo.

:: Wait for services to be ready
echo [INFO] Waiting for services to be ready...
set /a "attempts=0"
set /a "max_attempts=30"

:wait_backend
set /a "attempts+=1"
if %attempts% gtr %max_attempts% (
    echo [ERROR] Backend failed to start within expected time
    echo [INFO] Checking backend logs...
    docker-compose logs backend
    goto show_status
)

curl -f http://localhost:%BACKEND_PORT%/health >nul 2>&1
if %errorLevel% neq 0 (
    echo [INFO] Waiting for backend... (attempt %attempts%/%max_attempts%)
    timeout /t 10 /nobreak >nul
    goto wait_backend
)
echo [SUCCESS] Backend is ready

:: Wait for frontend
set /a "attempts=0"
:wait_frontend
set /a "attempts+=1"
if %attempts% gtr %max_attempts% (
    echo [ERROR] Frontend failed to start within expected time
    echo [INFO] Checking frontend logs...
    docker-compose logs frontend
    goto show_status
)

curl -f http://localhost:%FRONTEND_PORT% >nul 2>&1
if %errorLevel% neq 0 (
    echo [INFO] Waiting for frontend... (attempt %attempts%/%max_attempts%)
    timeout /t 10 /nobreak >nul
    goto wait_frontend
)
echo [SUCCESS] Frontend is ready
echo.

:: Run database migrations
echo [INFO] Running database migrations...
docker-compose exec -T backend npx prisma migrate deploy
if %errorLevel% neq 0 (
    echo [WARNING] Database migration failed or no migrations to run
) else (
    echo [SUCCESS] Database migrations completed
)

:: Generate Prisma client
echo [INFO] Generating Prisma client...
docker-compose exec -T backend npx prisma generate
if %errorLevel% neq 0 (
    echo [WARNING] Prisma client generation failed
) else (
    echo [SUCCESS] Prisma client generated
)
echo.

:show_status
:: Show service status
echo [INFO] Service Status:
echo ========================================
docker-compose ps
echo.

:: Show application URLs
echo [SUCCESS] Application is ready!
echo ========================================
echo Frontend:     http://localhost:%FRONTEND_PORT%
echo Backend API:  http://localhost:%BACKEND_PORT%
echo Health Check: http://localhost:%BACKEND_PORT%/health
echo ========================================
echo.

:: Check if services are actually responding
echo [INFO] Performing final health checks...
curl -f http://localhost:%BACKEND_PORT%/health >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Backend health check passed
) else (
    echo [WARNING] Backend health check failed
)

curl -f http://localhost:%FRONTEND_PORT% >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Frontend health check passed
) else (
    echo [WARNING] Frontend health check failed
)
echo.

:: Offer to open browser
echo [INFO] Would you like to open the application in your browser? (Y/N)
set /p "open_browser="
if /i "!open_browser!"=="Y" (
    echo [INFO] Opening application in default browser...
    start http://localhost:%FRONTEND_PORT%
)

:: Show management commands
echo.
echo [INFO] Management Commands:
echo ========================================
echo View logs:           docker-compose logs -f [service_name]
echo Stop application:    docker-compose down
echo Restart application: docker-compose restart
echo Update application:  git pull && docker-compose up -d --build
echo ========================================
echo.

:: Offer to show logs
echo [INFO] Would you like to view live logs? (Y/N)
set /p "show_logs="
if /i "!show_logs!"=="Y" (
    echo [INFO] Showing live logs (Press Ctrl+C to exit)...
    docker-compose logs -f
) else (
    echo [INFO] Application is running in the background
    echo [INFO] Use 'docker-compose logs -f' to view logs
    echo [INFO] Use 'docker-compose down' to stop the application
)

echo.
echo [SUCCESS] Startup completed successfully!
pause