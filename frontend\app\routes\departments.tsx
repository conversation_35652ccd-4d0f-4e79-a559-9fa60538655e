import { useState } from "react";
import { Plus, Building, Users, BookOpen, Edit, Trash2, Search } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";

// Mock data for demonstration
const mockDepartments = [
  {
    departmentID: "1",
    name: "Computer Science",
    code: "CS",
    instructorsCount: 25,
    coursesCount: 45,
    studentsCount: 320,
    createdAt: "2023-01-15",
  },
  {
    departmentID: "2",
    name: "Mathematics",
    code: "MATH",
    instructorsCount: 18,
    coursesCount: 32,
    studentsCount: 280,
    createdAt: "2023-01-15",
  },
  {
    departmentID: "3",
    name: "Physics",
    code: "PHYS",
    instructorsCount: 15,
    coursesCount: 28,
    studentsCount: 180,
    createdAt: "2023-01-15",
  },
  {
    departmentID: "4",
    name: "Chemistry",
    code: "CHEM",
    instructorsCount: 12,
    coursesCount: 24,
    studentsCount: 150,
    createdAt: "2023-01-15",
  },
];

export default function Departments() {
  const [departments, setDepartments] = useState(mockDepartments);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const filteredDepartments = departments.filter(dept =>
    dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Departments</h2>
          <p className="text-muted-foreground">
            Manage university departments and their resources
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Department
        </Button>
      </div>

      {/* Search Bar */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <input
            placeholder="Search departments..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8 w-full px-3 py-2 border border-input rounded-md"
          />
        </div>
      </div>

      {/* Departments Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredDepartments.map((department) => (
          <div
            key={department.departmentID}
            className="rounded-lg border bg-card text-card-foreground shadow-sm p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Building className="h-5 w-5 text-primary" />
                <div>
                  <h3 className="text-lg font-semibold">{department.name}</h3>
                  <p className="text-sm text-muted-foreground">{department.code}</p>
                </div>
              </div>
              <div className="flex space-x-1">
                <Button size="sm" variant="outline">
                  <Edit className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline">
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{department.instructorsCount}</div>
                <div className="text-xs text-muted-foreground">Instructors</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{department.coursesCount}</div>
                <div className="text-xs text-muted-foreground">Courses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{department.studentsCount}</div>
                <div className="text-xs text-muted-foreground">Students</div>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-xs text-muted-foreground">
                Created: {new Date(department.createdAt).toLocaleDateString()}
              </span>
              <Button size="sm" variant="outline">
                View Details
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Create Form Modal */}
      {showCreateForm && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Create New Department</h3>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Department Name</label>
                <input
                  type="text"
                  className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  placeholder="e.g., Computer Science"
                />
              </div>
              <div>
                <label className="text-sm font-medium">Department Code</label>
                <input
                  type="text"
                  className="w-full mt-1 px-3 py-2 border border-input rounded-md"
                  placeholder="e.g., CS"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
              <Button onClick={() => setShowCreateForm(false)}>
                Create Department
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Summary Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
          <div className="text-2xl font-bold text-center">{departments.length}</div>
          <div className="text-sm text-muted-foreground text-center">Total Departments</div>
        </div>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
          <div className="text-2xl font-bold text-center text-blue-600">
            {departments.reduce((sum, dept) => sum + dept.instructorsCount, 0)}
          </div>
          <div className="text-sm text-muted-foreground text-center">Total Instructors</div>
        </div>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
          <div className="text-2xl font-bold text-center text-green-600">
            {departments.reduce((sum, dept) => sum + dept.coursesCount, 0)}
          </div>
          <div className="text-sm text-muted-foreground text-center">Total Courses</div>
        </div>
        <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-4">
          <div className="text-2xl font-bold text-center text-purple-600">
            {departments.reduce((sum, dept) => sum + dept.studentsCount, 0)}
          </div>
          <div className="text-sm text-muted-foreground text-center">Total Students</div>
        </div>
      </div>
    </div>
  );
}