# University Schedule Assistant - REST API Design

## Base URL
```
https://api.schedule-assistant.university.edu/v1
```

## Authentication
All endpoints require JWT authentication with role-based access control:
- `admin`: Full access to all operations
- `scheduler`: Can manage schedules and view all data
- `instructor`: Can view own schedule and submit substitution requests
- `student`: Can view published schedules only

## Response Format
All responses follow this structure:
```json
{
  "success": boolean,
  "data": object | array,
  "message": string,
  "errors": array,
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "totalPages": number
  }
}
```

## Error Codes
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Authentication required
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource not found
- `409`: Conflict - Resource conflict (e.g., scheduling conflict)
- `422`: Unprocessable Entity - Validation errors
- `500`: Internal Server Error

---

## 1. CORE DATA MANAGEMENT ENDPOINTS

### 1.1 Semesters

#### GET /semesters
Get all semesters
```
Query Parameters:
- active: boolean (filter by active status)
- page: number (default: 1)
- limit: number (default: 20)
```

#### POST /semesters
Create a new semester
```json
{
  "name": "Fall 2024",
  "startDate": "2024-09-01",
  "endDate": "2024-12-15",
  "isActive": true
}
```

#### GET /semesters/{semesterId}
Get semester by ID

#### PUT /semesters/{semesterId}
Update semester

#### DELETE /semesters/{semesterId}
Delete semester (soft delete)

#### POST /semesters/{semesterId}/activate
Set semester as active (deactivates others)

### 1.2 Departments

#### GET /departments
Get all departments
```
Query Parameters:
- page: number
- limit: number
- search: string (search by name)
```

#### POST /departments
Create a new department
```json
{
  "name": "Computer Science",
  "code": "CS",
  "headInstructorId": "uuid"
}
```

#### GET /departments/{departmentId}
Get department by ID

#### PUT /departments/{departmentId}
Update department

#### DELETE /departments/{departmentId}
Delete department

#### GET /departments/{departmentId}/instructors
Get all instructors in department

#### GET /departments/{departmentId}/courses
Get all courses in department

### 1.3 Rooms

#### GET /rooms
Get all rooms
```
Query Parameters:
- type: string (filter by room type)
- building: string
- minCapacity: number
- maxCapacity: number
- departmentId: string
- available: boolean
- page: number
- limit: number
```

#### POST /rooms
Create a new room
```json
{
  "roomNumber": "CS-101",
  "maxCapacity": 50,
  "type": "Classroom",
  "assignedDepartmentId": "uuid",
  "building": "Computer Science Building",
  "floorNumber": 1,
  "equipmentNotes": "Projector, Whiteboard"
}
```

#### GET /rooms/{roomId}
Get room by ID

#### PUT /rooms/{roomId}
Update room

#### DELETE /rooms/{roomId}
Delete room

#### GET /rooms/{roomId}/availability
Get room availability for a specific time period
```
Query Parameters:
- startDate: date
- endDate: date
- dayOfWeek: string (optional)
```

### 1.4 Instructors

#### GET /instructors
Get all instructors
```
Query Parameters:
- departmentId: string
- active: boolean
- available: boolean (for current time)
- page: number
- limit: number
- search: string
```

#### POST /instructors
Create a new instructor
```json
{
  "name": "Dr. John Smith",
  "email": "<EMAIL>",
  "departmentId": "uuid",
  "availability": {
    "Monday": [1,2,3,4,5,6,7,8],
    "Tuesday": [1,2,3,4,5,6,7,8],
    "Wednesday": [],
    "Thursday": [1,2,3,4,5,6,7,8],
    "Friday": [1,2,3,4,5,6,7,8],
    "Saturday": [],
    "Sunday": []
  },
  "maxDailySlots": 20,
  "preferredTimes": {}
}
```

#### GET /instructors/{instructorId}
Get instructor by ID

#### PUT /instructors/{instructorId}
Update instructor

#### DELETE /instructors/{instructorId}
Delete instructor

#### PUT /instructors/{instructorId}/availability
Update instructor availability
```json
{
  "availability": {
    "Monday": [1,2,3,4,5,6,7,8],
    "Tuesday": [1,2,3,4,5,6,7,8]
  }
}
```

#### GET /instructors/{instructorId}/schedule
Get instructor's schedule
```
Query Parameters:
- semesterId: string
- startDate: date
- endDate: date
```

#### GET /instructors/{instructorId}/workload
Get instructor's workload statistics
```
Query Parameters:
- semesterId: string
```

### 1.5 Student Levels

#### GET /student-levels
Get all student levels
```
Query Parameters:
- departmentId: string
- page: number
- limit: number
```

#### POST /student-levels
Create a new student level
```json
{
  "name": "First Year Pharmacy",
  "departmentId": "uuid",
  "expectedStudentCount": 120,
  "yearNumber": 1,
  "semesterNumber": 1
}
```

#### GET /student-levels/{levelId}
Get student level by ID

#### PUT /student-levels/{levelId}
Update student level

#### DELETE /student-levels/{levelId}
Delete student level

#### GET /student-levels/{levelId}/groups
Get all groups for a student level

### 1.6 Student Groups

#### GET /student-groups
Get all student groups
```
Query Parameters:
- levelId: string
- groupType: string
- parentGroupId: string
- page: number
- limit: number
```

#### POST /student-groups
Create a new student group
```json
{
  "levelId": "uuid",
  "groupName": "Group A",
  "parentGroupId": "uuid",
  "groupType": "Group",
  "studentCount": 30
}
```

#### GET /student-groups/{groupId}
Get student group by ID

#### PUT /student-groups/{groupId}
Update student group

#### DELETE /student-groups/{groupId}
Delete student group

#### GET /student-groups/{groupId}/schedule
Get group's schedule
```
Query Parameters:
- semesterId: string
- startDate: date
- endDate: date
```

### 1.7 Courses

#### GET /courses
Get all courses
```
Query Parameters:
- departmentId: string
- active: boolean
- search: string
- page: number
- limit: number
```

#### POST /courses
Create a new course
```json
{
  "courseCode": "CS101",
  "title": "Introduction to Computer Science",
  "departmentId": "uuid",
  "credits": 3,
  "description": "Basic concepts of computer science"
}
```

#### GET /courses/{courseId}
Get course by ID

#### PUT /courses/{courseId}
Update course

#### DELETE /courses/{courseId}
Delete course

#### GET /courses/{courseId}/lessons
Get all lessons for a course
```
Query Parameters:
- semesterId: string
```

### 1.8 Lessons

#### GET /lessons
Get all lessons
```
Query Parameters:
- courseId: string
- instructorId: string
- studentGroupId: string
- semesterId: string
- lessonType: string
- scheduled: boolean (whether lesson is scheduled)
- page: number
- limit: number
```

#### POST /lessons
Create a new lesson
```json
{
  "courseId": "uuid",
  "studentGroupId": "uuid",
  "instructorId": "uuid",
  "durationInSlots": 4,
  "lessonType": "Lecture",
  "requiredRoomType": "Classroom",
  "semesterId": "uuid",
  "sessionsPerWeek": 2,
  "totalSessions": 30,
  "priority": 5,
  "notes": "Requires projector"
}
```

#### GET /lessons/{lessonId}
Get lesson by ID

#### PUT /lessons/{lessonId}
Update lesson

#### DELETE /lessons/{lessonId}
Delete lesson

#### POST /lessons/bulk
Create multiple lessons
```json
{
  "lessons": [
    {
      "courseId": "uuid",
      "studentGroupId": "uuid",
      "instructorId": "uuid",
      "durationInSlots": 4,
      "lessonType": "Lecture",
      "requiredRoomType": "Classroom",
      "semesterId": "uuid"
    }
  ]
}
```

---

## 2. WORKFLOW MANAGEMENT ENDPOINTS

### 2.1 Generation Sessions

#### GET /generation-sessions
Get all generation sessions
```
Query Parameters:
- semesterId: string
- status: string
- page: number
- limit: number
```

#### POST /generation-sessions
Create a new generation session
```json
{
  "semesterId": "uuid"
}
```

#### GET /generation-sessions/{sessionId}
Get generation session by ID

#### PUT /generation-sessions/{sessionId}/status
Update session status
```json
{
  "status": "MANIFEST_GENERATED"
}
```

#### DELETE /generation-sessions/{sessionId}
Delete generation session

### 2.2 Scheduling Manifest

#### POST /generation-sessions/{sessionId}/generate-manifest
Generate scheduling manifest (Excel file)
```
Response: File download
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
```

#### POST /generation-sessions/{sessionId}/upload-manifest
Upload modified manifest
```
Content-Type: multipart/form-data
Body: manifest file
```

#### GET /generation-sessions/{sessionId}/manifest
Download current manifest
```
Response: File download
```

#### POST /generation-sessions/{sessionId}/validate-manifest
Validate uploaded manifest
```json
{
  "manifestData": "base64-encoded-excel-data"
}
```

### 2.3 Schedule Generation

#### POST /generation-sessions/{sessionId}/start-generation
Start automatic schedule generation
```json
{
  "useUploadedManifest": boolean,
  "constraints": {
    "hardConstraints": ["uniqueness", "capacity", "roomType", "availability"],
    "softConstraints": ["minimizeGaps", "compactness", "preferences"]
  }
}
```

#### GET /generation-sessions/{sessionId}/progress
Get generation progress
```
Response:
{
  "status": "SCHEDULING_IN_PROGRESS",
  "progress": 65,
  "currentPhase": "Assigning rooms",
  "lessonsScheduled": 150,
  "totalLessons": 230,
  "estimatedTimeRemaining": "5 minutes"
}
```

#### POST /generation-sessions/{sessionId}/stop-generation
Stop generation process

#### GET /generation-sessions/{sessionId}/results
Get generation results
```
Response:
{
  "status": "SCHEDULING_COMPLETED",
  "totalLessons": 230,
  "scheduledLessons": 225,
  "unscheduledLessons": 5,
  "constraintViolations": [],
  "unscheduledLessonDetails": [],
  "generationTime": "12 minutes",
  "qualityScore": 85
}
```

---

## 3. SCHEDULE MANAGEMENT ENDPOINTS

### 3.1 Schedule Entries

#### GET /schedule-entries
Get schedule entries
```
Query Parameters:
- semesterId: string
- dayOfWeek: string
- startSlot: number
- endSlot: number
- roomId: string
- instructorId: string
- studentGroupId: string
- lessonId: string
- weekNumber: number
- locked: boolean
- page: number
- limit: number
```

#### POST /schedule-entries
Create a schedule entry (manual scheduling)
```json
{
  "lessonId": "uuid",
  "roomId": "uuid",
  "dayOfWeek": "Monday",
  "startSlot": 5,
  "endSlot": 8,
  "semesterId": "uuid",
  "weekNumber": 1
}
```

#### GET /schedule-entries/{entryId}
Get schedule entry by ID

#### PUT /schedule-entries/{entryId}
Update schedule entry
```json
{
  "roomId": "uuid",
  "dayOfWeek": "Tuesday",
  "startSlot": 10,
  "endSlot": 13
}
```

#### DELETE /schedule-entries/{entryId}
Delete schedule entry

#### PUT /schedule-entries/{entryId}/lock
Lock/unlock schedule entry
```json
{
  "isLocked": true
}
```

#### POST /schedule-entries/move
Move schedule entry to new time/room
```json
{
  "entryId": "uuid",
  "newRoomId": "uuid",
  "newDayOfWeek": "Wednesday",
  "newStartSlot": 15,
  "newEndSlot": 18,
  "checkConflicts": true
}
```

#### POST /schedule-entries/swap
Swap two schedule entries
```json
{
  "entryId1": "uuid",
  "entryId2": "uuid",
  "checkConflicts": true
}
```

### 3.2 Conflict Detection

#### POST /schedule-entries/check-conflicts
Check for scheduling conflicts
```json
{
  "lessonId": "uuid",
  "roomId": "uuid",
  "dayOfWeek": "Monday",
  "startSlot": 5,
  "endSlot": 8,
  "semesterId": "uuid"
}
```

#### GET /conflicts
Get all current conflicts
```
Query Parameters:
- semesterId: string
- severity: string (hard, soft)
- resolved: boolean
```

#### POST /conflicts/{conflictId}/resolve
Mark conflict as resolved
```json
{
  "resolution": "Manual override approved",
  "resolvedBy": "uuid"
}
```

### 3.3 Schedule Views

#### GET /schedules/instructor/{instructorId}
Get instructor's complete schedule
```
Query Parameters:
- semesterId: string
- startDate: date
- endDate: date
- format: string (json, pdf, excel)
```

#### GET /schedules/room/{roomId}
Get room's complete schedule
```
Query Parameters:
- semesterId: string
- startDate: date
- endDate: date
- format: string (json, pdf, excel)
```

#### GET /schedules/student-group/{groupId}
Get student group's complete schedule
```
Query Parameters:
- semesterId: string
- startDate: date
- endDate: date
- format: string (json, pdf, excel)
```

#### GET /schedules/department/{departmentId}
Get department's complete schedule
```
Query Parameters:
- semesterId: string
- startDate: date
- endDate: date
- format: string (json, pdf, excel)
```

#### GET /schedules/grid
Get schedule in grid format
```
Query Parameters:
- semesterId: string
- view: string (weekly, daily)
- startDate: date
- endDate: date
- filterBy: string (instructor, room, group)
- filterId: string
```

---

## 4. SUBSTITUTION MANAGEMENT ENDPOINTS

### 4.1 Substitutions

#### GET /substitutions
Get all substitutions
```
Query Parameters:
- date: date
- status: string
- absentInstructorId: string
- substituteInstructorId: string
- page: number
- limit: number
```

#### POST /substitutions
Create a substitution request
```json
{
  "originalEntryId": "uuid",
  "absentInstructorId": "uuid",
  "substitutionDate": "2024-03-15",
  "reason": "Medical leave",
  "notes": "Urgent replacement needed"
}
```

#### GET /substitutions/{substitutionId}
Get substitution by ID

#### PUT /substitutions/{substitutionId}
Update substitution
```json
{
  "substituteInstructorId": "uuid",
  "status": "APPROVED",
  "notes": "Dr. Smith will cover the class"
}
```

#### DELETE /substitutions/{substitutionId}
Delete substitution

#### POST /substitutions/{substitutionId}/approve
Approve substitution
```json
{
  "approvedBy": "uuid",
  "notes": "Approved by department head"
}
```

#### POST /substitutions/{substitutionId}/reject
Reject substitution
```json
{
  "rejectedBy": "uuid",
  "reason": "No suitable substitute available"
}
```

### 4.2 Substitute Suggestions

#### GET /substitutions/{substitutionId}/suggestions
Get substitute instructor suggestions
```
Query Parameters:
- limit: number (default: 10)
```

#### POST /substitutions/find-substitutes
Find available substitutes for a specific time
```json
{
  "dayOfWeek": "Monday",
  "startSlot": 5,
  "endSlot": 8,
  "date": "2024-03-15",
  "requiredDepartmentId": "uuid",
  "courseId": "uuid"
}
```

### 4.3 Daily Bulletins

#### GET /substitutions/daily-bulletin
Get daily substitution bulletin
```
Query Parameters:
- date: date (default: today)
- format: string (json, pdf)
```

#### POST /substitutions/daily-bulletin/publish
Publish daily bulletin
```json
{
  "date": "2024-03-15",
  "recipients": ["<EMAIL>", "<EMAIL>"]
}
```

---

## 5. CONSTRAINT MANAGEMENT ENDPOINTS

### 5.1 Scheduling Constraints

#### GET /constraints
Get all scheduling constraints
```
Query Parameters:
- type: string (HARD, SOFT)
- category: string
- active: boolean
- page: number
- limit: number
```

#### POST /constraints
Create a new constraint
```json
{
  "name": "No Friday Afternoon Labs",
  "description": "Lab sessions should not be scheduled on Friday afternoons",
  "constraintType": "SOFT",
  "constraintCategory": "TIME_PLACEMENT",
  "ruleDefinition": {
    "type": "time_restriction",
    "lessonTypes": ["Lab"],
    "excludedTimes": {
      "Friday": [19, 20, 21, 22, 23, 24, 25, 26]
    }
  },
  "weight": 8
}
```

#### GET /constraints/{constraintId}
Get constraint by ID

#### PUT /constraints/{constraintId}
Update constraint

#### DELETE /constraints/{constraintId}
Delete constraint

#### PUT /constraints/{constraintId}/toggle
Enable/disable constraint
```json
{
  "isActive": false
}
```

### 5.2 Constraint Validation

#### POST /constraints/validate
Validate constraint rule definition
```json
{
  "ruleDefinition": {
    "type": "contiguity",
    "lessons": ["uuid1", "uuid2"],
    "relationship": "same_day"
  }
}
```

#### GET /constraints/violations
Get current constraint violations
```
Query Parameters:
- semesterId: string
- constraintId: string
- severity: string
```

---

## 6. BULK OPERATIONS & EXCEL INTEGRATION

### 6.1 Bulk Import

#### POST /bulk/import/instructors
Import instructors from Excel
```
Content-Type: multipart/form-data
Body: Excel file with instructor data
```

#### POST /bulk/import/rooms
Import rooms from Excel

#### POST /bulk/import/courses
Import courses from Excel

#### POST /bulk/import/student-levels
Import student levels from Excel

#### POST /bulk/import/student-groups
Import student groups from Excel

#### POST /bulk/import/lessons
Import lessons from Excel

### 6.2 Bulk Export

#### GET /bulk/export/instructors
Export instructors to Excel
```
Query Parameters:
- departmentId: string
- format: string (xlsx, csv)
```

#### GET /bulk/export/rooms
Export rooms to Excel

#### GET /bulk/export/courses
Export courses to Excel

#### GET /bulk/export/student-levels
Export student levels to Excel

#### GET /bulk/export/student-groups
Export student groups to Excel

#### GET /bulk/export/lessons
Export lessons to Excel

#### GET /bulk/export/complete-schedule
Export complete schedule to Excel
```
Query Parameters:
- semesterId: string
- format: string (xlsx, pdf)
- view: string (by_instructor, by_room, by_group, grid)
```

---

## 7. REPORTING & ANALYTICS ENDPOINTS

### 7.1 Schedule Analytics

#### GET /analytics/utilization/rooms
Get room utilization statistics
```
Query Parameters:
- semesterId: string
- startDate: date
- endDate: date
- departmentId: string
```

#### GET /analytics/utilization/instructors
Get instructor workload statistics

#### GET /analytics/distribution/lessons
Get lesson distribution analytics

#### GET /analytics/conflicts/summary
Get conflict summary report

#### GET /analytics/quality/score
Get schedule quality metrics
```
Response:
{
  "overallScore": 85,
  "metrics": {
    "gapMinimization": 90,
    "compactness": 80,
    "resourceUtilization": 85,
    "constraintSatisfaction": 95
  }
}
```

### 7.2 Custom Reports

#### POST /reports/custom
Generate custom report
```json
{
  "reportType": "instructor_workload",
  "parameters": {
    "semesterId": "uuid",
    "departmentId": "uuid",
    "format": "pdf"
  },
  "filters": {
    "minWorkload": 10,
    "maxWorkload": 25
  }
}
```

#### GET /reports/{reportId}
Get generated report

#### GET /reports
Get all available reports
```
Query Parameters:
- type: string
- status: string
- createdBy: string
```

---

## 8. SYSTEM ADMINISTRATION ENDPOINTS

### 8.1 System Configuration

#### GET /admin/config
Get system configuration

#### PUT /admin/config
Update system configuration
```json
{
  "timeSlots": {
    "startTime": "09:30",
    "endTime": "16:00",
    "slotDuration": 15
  },
  "workingDays": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
  "defaultConstraints": ["uniqueness", "capacity", "roomType", "availability"]
}
```

### 8.2 Data Management

#### POST /admin/data/backup
Create system backup

#### POST /admin/data/restore
Restore from backup

#### DELETE /admin/data/cleanup
Clean up old data
```json
{
  "olderThan": "2023-01-01",
  "dataTypes": ["generation_sessions", "old_schedules"]
}
```

### 8.3 User Management

#### GET /admin/users
Get all users

#### POST /admin/users
Create new user

#### PUT /admin/users/{userId}/role
Update user role

#### DELETE /admin/users/{userId}
Delete user

---

## 9. REAL-TIME FEATURES

### 9.1 WebSocket Events

#### Connection: `/ws/schedule-updates`
Real-time schedule updates

Events:
- `schedule_entry_created`
- `schedule_entry_updated`
- `schedule_entry_deleted`
- `generation_progress`
- `conflict_detected`
- `substitution_created`

### 9.2 Notifications

#### GET /notifications
Get user notifications

#### PUT /notifications/{notificationId}/read
Mark notification as read

#### POST /notifications/subscribe
Subscribe to notification types
```json
{
  "types": ["schedule_conflicts", "substitution_requests", "generation_complete"]
}
```