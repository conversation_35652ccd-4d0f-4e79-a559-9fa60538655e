import { useState } from "react";
import { Download, FileSpreadsheet, Calendar, Building, Users, BookOpen } from "lucide-react";
import { Button } from "~/components/ui/button";

interface ExcelExportProps {
  semesterID?: string;
  semesterName?: string;
}

export function ExcelExport({ semesterID, semesterName }: ExcelExportProps) {
  const [isExporting, setIsExporting] = useState<string | null>(null);

  const exportData = async (type: string, filename: string) => {
    setIsExporting(type);

    try {
      const params = new URLSearchParams({
        type,
        ...(semesterID && { semesterID })
      });

      const response = await fetch(`/api/excel/export?${params}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        console.error('Export failed:', response.statusText);
      }
    } catch (error) {
      console.error('Error exporting data:', error);
    } finally {
      setIsExporting(null);
    }
  };

  const exportOptions = [
    {
      type: 'schedule',
      title: 'Schedule',
      description: 'Export complete schedule with all lessons',
      icon: Calendar,
      filename: `Schedule_${semesterName || 'Unknown'}_${new Date().toISOString().split('T')[0]}.xlsx`,
      requiresSemester: true,
      color: 'text-blue-600'
    },
    {
      type: 'departments',
      title: 'Departments',
      description: 'Export all departments data',
      icon: Building,
      filename: `Departments_${new Date().toISOString().split('T')[0]}.xlsx`,
      requiresSemester: false,
      color: 'text-green-600'
    },
    {
      type: 'rooms',
      title: 'Rooms',
      description: 'Export all rooms and equipment data',
      icon: Building,
      filename: `Rooms_${new Date().toISOString().split('T')[0]}.xlsx`,
      requiresSemester: false,
      color: 'text-purple-600'
    },
    {
      type: 'instructors',
      title: 'Instructors',
      description: 'Export all instructors data',
      icon: Users,
      filename: `Instructors_${new Date().toISOString().split('T')[0]}.xlsx`,
      requiresSemester: false,
      color: 'text-orange-600'
    },
    {
      type: 'courses',
      title: 'Courses',
      description: 'Export courses for selected semester',
      icon: BookOpen,
      filename: `Courses_${semesterName || 'Unknown'}_${new Date().toISOString().split('T')[0]}.xlsx`,
      requiresSemester: true,
      color: 'text-red-600'
    }
  ];

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">Export to Excel</h3>
        <p className="text-sm text-muted-foreground">
          Download data in Excel format for external use or backup
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {exportOptions.map((option) => {
          const Icon = option.icon;
          const canExport = !option.requiresSemester || semesterID;

          return (
            <div
              key={option.type}
              className={`rounded-lg border p-4 ${
                canExport ? 'hover:shadow-md transition-shadow' : 'opacity-50'
              }`}
            >
              <div className="flex items-start space-x-3">
                <Icon className={`h-6 w-6 ${option.color} mt-1`} />
                <div className="flex-1">
                  <h4 className="font-medium">{option.title}</h4>
                  <p className="text-sm text-muted-foreground mt-1">
                    {option.description}
                  </p>

                  {option.requiresSemester && !semesterID && (
                    <p className="text-xs text-red-600 mt-2">
                      Requires semester selection
                    </p>
                  )}

                  <Button
                    size="sm"
                    variant="outline"
                    className="mt-3 w-full"
                    disabled={!canExport || isExporting === option.type}
                    onClick={() => exportData(option.type, option.filename)}
                  >
                    {isExporting === option.type ? (
                      <>
                        <Download className="mr-2 h-3 w-3 animate-spin" />
                        Exporting...
                      </>
                    ) : (
                      <>
                        <Download className="mr-2 h-3 w-3" />
                        Export
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Export Information */}
      <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
        <div className="flex items-start space-x-3">
          <FileSpreadsheet className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-800">Export Information</h4>
            <div className="text-sm text-blue-700 mt-1 space-y-1">
              <p>• All exports are in Excel (.xlsx) format</p>
              <p>• Schedule exports include summary statistics</p>
              <p>• Files are automatically named with current date</p>
              <p>• Large datasets may take a few moments to generate</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}