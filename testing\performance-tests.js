/**
 * Performance Testing Suite for University Schedule Assistant
 * Tests application performance under various load conditions
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
    baseURL: process.env.TEST_BASE_URL || 'http://localhost:3001',
    frontendURL: process.env.TEST_FRONTEND_URL || 'http://localhost:3000',
    concurrentUsers: parseInt(process.env.CONCURRENT_USERS) || 10,
    testDuration: parseInt(process.env.TEST_DURATION) || 60000, // 1 minute
    rampUpTime: parseInt(process.env.RAMP_UP_TIME) || 10000, // 10 seconds
};

// Test results storage
const results = {
    startTime: null,
    endTime: null,
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    minResponseTime: Infinity,
    maxResponseTime: 0,
    responseTimes: [],
    errors: [],
    throughput: 0
};

// Utility functions
const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

const logResult = (message) => {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] ${message}`);
};

const makeRequest = async (method, endpoint, data = null) => {
    const startTime = Date.now();

    try {
        const config = {
            method,
            url: `${CONFIG.baseURL}${endpoint}`,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'Performance-Test-Suite'
            }
        };

        if (data) {
            config.data = data;
        }

        const response = await axios(config);
        const responseTime = Date.now() - startTime;

        // Record metrics
        results.totalRequests++;
        results.successfulRequests++;
        results.responseTimes.push(responseTime);
        results.minResponseTime = Math.min(results.minResponseTime, responseTime);
        results.maxResponseTime = Math.max(results.maxResponseTime, responseTime);

        return { success: true, responseTime, status: response.status };

    } catch (error) {
        const responseTime = Date.now() - startTime;

        results.totalRequests++;
        results.failedRequests++;
        results.errors.push({
            endpoint,
            error: error.message,
            responseTime,
            timestamp: new Date().toISOString()
        });

        return { success: false, responseTime, error: error.message };
    }
};

// Test scenarios
const testScenarios = {
    // Health check test
    healthCheck: async () => {
        return await makeRequest('GET', '/health');
    },

    // Department operations
    getDepartments: async () => {
        return await makeRequest('GET', '/api/departments');
    },

    createDepartment: async () => {
        const testData = {
            departmentName: `Test Department ${Date.now()}`,
            departmentCode: `TEST${Math.random().toString(36).substr(2, 5).toUpperCase()}`
        };
        return await makeRequest('POST', '/api/departments', testData);
    },

    // Room operations
    getRooms: async () => {
        return await makeRequest('GET', '/api/rooms');
    },

    createRoom: async () => {
        const testData = {
            roomName: `Test Room ${Date.now()}`,
            capacity: Math.floor(Math.random() * 50) + 10,
            roomType: 'Classroom'
        };
        return await makeRequest('POST', '/api/rooms', testData);
    },

    // Instructor operations
    getInstructors: async () => {
        return await makeRequest('GET', '/api/instructors');
    },

    // Course operations
    getCourses: async () => {
        return await makeRequest('GET', '/api/courses');
    },

    // Schedule operations
    getSchedule: async () => {
        return await makeRequest('GET', '/api/scheduling/schedule');
    },

    // Excel operations
    getExcelTemplates: async () => {
        return await makeRequest('GET', '/api/excel/templates');
    }
};

// Load testing functions
const runSingleUserTest = async (duration) => {
    const endTime = Date.now() + duration;
    const scenarios = Object.values(testScenarios);

    while (Date.now() < endTime) {
        // Pick random scenario
        const scenario = scenarios[Math.floor(Math.random() * scenarios.length)];
        await scenario();

        // Random delay between requests (100-500ms)
        await sleep(Math.random() * 400 + 100);
    }
};

const runConcurrentUserTest = async (userCount, duration) => {
    logResult(`Starting concurrent user test: ${userCount} users for ${duration}ms`);

    const userPromises = [];
    const rampUpDelay = CONFIG.rampUpTime / userCount;

    for (let i = 0; i < userCount; i++) {
        userPromises.push(
            sleep(i * rampUpDelay).then(() => runSingleUserTest(duration))
        );
    }

    await Promise.all(userPromises);
};

// Stress testing
const runStressTest = async () => {
    logResult('Starting stress test...');

    const stressLevels = [1, 5, 10, 20, 50];
    const testDuration = 30000; // 30 seconds per level

    for (const userCount of stressLevels) {
        logResult(`Stress test level: ${userCount} concurrent users`);

        const levelStartTime = Date.now();
        await runConcurrentUserTest(userCount, testDuration);
        const levelEndTime = Date.now();

        // Calculate metrics for this level
        const levelDuration = levelEndTime - levelStartTime;
        const throughput = (results.totalRequests / levelDuration) * 1000; // requests per second

        logResult(`Level ${userCount} completed - Throughput: ${throughput.toFixed(2)} req/s`);

        // Brief pause between levels
        await sleep(5000);
    }
};