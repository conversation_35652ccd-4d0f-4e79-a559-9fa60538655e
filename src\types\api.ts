import { Request, Response } from 'express';
import {
  UserRole,
  RoomType,
  LessonType,
  GroupType,
  ScheduleStatus,
  DayOfWeek,
  SubstitutionStatus,
  ConstraintType,
  GenerationStatus
} from '@prisma/client';

// =============================================
// API Response Types
// =============================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

export interface PaginationQuery {
  page?: number;
  limit?: number;
}

// =============================================
// Authentication Types
// =============================================

export interface AuthenticatedRequest extends Request {
  user?: {
    userID: string;
    username: string;
    email: string;
    role: UserRole;
    departmentID?: string;
    instructorID?: string;
  };
}

export interface JWTPayload {
  userID: string;
  username: string;
  email: string;
  role: UserRole;
  departmentID?: string;
  instructorID?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: {
    userID: string;
    username: string;
    email: string;
    role: UserRole;
    departmentID?: string;
    instructorID?: string;
  };
  accessToken: string;
  refreshToken: string;
}

// =============================================
// Core Entity DTOs
// =============================================

export interface CreateSemesterDTO {
  name: string;
  startDate: string;
  endDate: string;
  isActive?: boolean;
}

export interface UpdateSemesterDTO extends Partial<CreateSemesterDTO> {}

export interface CreateDepartmentDTO {
  name: string;
  code?: string;
}

export interface UpdateDepartmentDTO extends Partial<CreateDepartmentDTO> {}

export interface CreateRoomDTO {
  roomNumber: string;
  maxCapacity: number;
  type: RoomType;
  assignedDepartmentID?: string;
  building?: string;
  floorNumber?: number;
  equipmentNotes?: string;
}

export interface UpdateRoomDTO extends Partial<CreateRoomDTO> {}

export interface CreateInstructorDTO {
  name: string;
  email?: string;
  departmentID: string;
  maxDailySlots?: number;
}

export interface UpdateInstructorDTO extends Partial<CreateInstructorDTO> {}

export interface InstructorAvailabilityDTO {
  dayOfWeek: string;
  availableSlots: number[];
}

export interface CreateStudentLevelDTO {
  name: string;
  departmentID: string;
  expectedStudentCount: number;
  yearNumber?: number;
  semesterNumber?: number;
}

export interface UpdateStudentLevelDTO extends Partial<CreateStudentLevelDTO> {}

export interface CreateStudentGroupDTO {
  levelID: string;
  groupName: string;
  parentGroupID?: string;
  groupType: GroupType;
  studentCount: number;
}

export interface UpdateStudentGroupDTO extends Partial<CreateStudentGroupDTO> {}

export interface CreateCourseDTO {
  courseCode: string;
  title: string;
  departmentID: string;
  credits?: number;
  description?: string;
}

export interface UpdateCourseDTO extends Partial<CreateCourseDTO> {}

export interface CreateLessonDTO {
  courseID: string;
  studentGroupID: string;
  instructorID: string;
  durationInSlots: number;
  lessonType: LessonType;
  requiredRoomType: RoomType;
  semesterID: string;
  sessionsPerWeek?: number;
  totalSessions?: number;
  priority?: number;
  specialRequirements?: string;
  notes?: string;
}

export interface UpdateLessonDTO extends Partial<CreateLessonDTO> {}

// =============================================
// Scheduling Types
// =============================================

export interface CreateScheduledLessonDTO {
  lessonID: string;
  roomID: string;
  dayOfWeek: DayOfWeek;
  startSlot: number;
  endSlot: number;
  semesterID: string;
  weekNumber?: number;
}

export interface UpdateScheduledLessonDTO extends Partial<CreateScheduledLessonDTO> {}

export interface MoveScheduledLessonDTO {
  entryId: string;
  newRoomId?: string;
  newDayOfWeek?: DayOfWeek;
  newStartSlot?: number;
  newEndSlot?: number;
  checkConflicts?: boolean;
}

export interface SwapScheduledLessonsDTO {
  entryId1: string;
  entryId2: string;
  checkConflicts?: boolean;
}

export interface ConflictCheckDTO {
  lessonID: string;
  roomID: string;
  dayOfWeek: DayOfWeek;
  startSlot: number;
  endSlot: number;
  semesterID: string;
}

export interface ConflictInfo {
  type: 'room' | 'instructor' | 'student_group';
  conflictingEntryId: string;
  message: string;
  severity: 'hard' | 'soft';
}

// =============================================
// Substitution Types
// =============================================

export interface CreateSubstitutionDTO {
  originalInstructorID: string;
  scheduledLessonID: string;
  dateOfAbsence: string;
  reason?: string;
  notes?: string;
}

export interface UpdateSubstitutionDTO {
  replacementInstructorID?: string;
  status?: SubstitutionStatus;
  notes?: string;
}

export interface SubstituteSuggestion {
  instructorID: string;
  instructorName: string;
  departmentName: string;
  qualificationScore: number;
  availabilityScore: number;
  workloadScore: number;
  overallScore: number;
  notes?: string;
}

// =============================================
// Workflow Types
// =============================================

export interface CreateGenerationSessionDTO {
  semesterID: string;
}

export interface GenerationProgressInfo {
  status: GenerationStatus;
  progress: number;
  currentPhase: string;
  lessonsScheduled: number;
  totalLessons: number;
  estimatedTimeRemaining?: string;
  qualityScore?: number;
}

export interface GenerationResultInfo {
  status: GenerationStatus;
  totalLessons: number;
  scheduledLessons: number;
  unscheduledLessons: number;
  constraintViolations: any[];
  unscheduledLessonDetails: any[];
  generationTime: string;
  qualityScore: number;
}

export interface StartGenerationDTO {
  useUploadedManifest?: boolean;
  constraints?: {
    hardConstraints: string[];
    softConstraints: string[];
  };
}

// =============================================
// Constraint Types
// =============================================

export interface CreateConstraintDTO {
  name: string;
  description?: string;
  constraintType: ConstraintType;
  ruleDefinition: any;
  weight?: number;
  semesterID?: string;
}

export interface UpdateConstraintDTO extends Partial<CreateConstraintDTO> {}

// =============================================
// Query Filter Types
// =============================================

export interface SemesterFilters extends PaginationQuery {
  active?: boolean;
}

export interface DepartmentFilters extends PaginationQuery {
  search?: string;
}

export interface RoomFilters extends PaginationQuery {
  type?: RoomType;
  building?: string;
  minCapacity?: number;
  maxCapacity?: number;
  departmentId?: string;
  available?: boolean;
}

export interface InstructorFilters extends PaginationQuery {
  departmentId?: string;
  active?: boolean;
  available?: boolean;
  search?: string;
}

export interface LessonFilters extends PaginationQuery {
  courseId?: string;
  instructorId?: string;
  studentGroupId?: string;
  semesterId?: string;
  lessonType?: LessonType;
  scheduled?: boolean;
}

export interface ScheduledLessonFilters extends PaginationQuery {
  semesterId?: string;
  dayOfWeek?: DayOfWeek;
  startSlot?: number;
  endSlot?: number;
  roomId?: string;
  instructorId?: string;
  studentGroupId?: string;
  lessonId?: string;
  weekNumber?: number;
  locked?: boolean;
}

export interface SubstitutionFilters extends PaginationQuery {
  date?: string;
  status?: SubstitutionStatus;
  absentInstructorId?: string;
  substituteInstructorId?: string;
}

// =============================================
// Utility Types
// =============================================

export interface TimeSlot {
  slot: number;
  startTime: string;
  endTime: string;
  label: string;
}

export interface ScheduleGridEntry {
  scheduledLessonID: string;
  lessonID: string;
  courseCode: string;
  courseTitle: string;
  instructorName: string;
  studentGroupName: string;
  roomNumber: string;
  dayOfWeek: DayOfWeek;
  startSlot: number;
  endSlot: number;
  duration: number;
  lessonType: LessonType;
  status: ScheduleStatus;
  isLocked: boolean;
}

export interface BulkImportResult {
  totalRecords: number;
  successfulImports: number;
  failedImports: number;
  errors: Array<{
    row: number;
    field: string;
    message: string;
  }>;
}

// =============================================
// Express Handler Types
// =============================================

export type AsyncRequestHandler<T = any> = (
  req: AuthenticatedRequest,
  res: Response
) => Promise<void>;

export type RequestHandler<T = any> = (
  req: AuthenticatedRequest,
  res: Response
) => void;