@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Main Launcher
:: This is the main entry point for all application management

echo ========================================
echo University Schedule Assistant
echo ========================================
echo.

:: Set color for better visibility
color 0A

echo [INFO] Welcome to University Schedule Assistant!
echo.

echo What would you like to do?
echo.
echo 1. Application Management (Start, Stop, Status, etc.)
echo 2. Maintenance & Troubleshooting Tools
echo 3. Quick Start (Start Application)
echo 4. Quick Status Check
echo 5. Exit
echo.

set /p "choice=Choose option (1-5): "

if "!choice!"=="1" (
    echo [INFO] Opening Application Manager...
    call app-manager.bat
) else if "!choice!"=="2" (
    echo [INFO] Opening Maintenance Tools...
    call maintenance-tools.bat
) else if "!choice!"=="3" (
    echo [INFO] Quick starting application...
    call app-manager.bat
    :: Simulate choosing option 1 (Start Application)
    echo 1 | call app-manager.bat
) else if "!choice!"=="4" (
    echo [INFO] Quick status check...
    echo.
    echo ========================================
    echo Quick Status Check
    echo ========================================
    echo.

    :: Check Docker
    docker info >nul 2>&1
    if %errorLevel% equ 0 (
        echo [SUCCESS] Docker is running
    ) else (
        echo [ERROR] Docker is not running
    )

    :: Check containers
    echo [INFO] Container Status:
    docker-compose ps

    :: Check services
    echo [INFO] Service Health:
    curl -f http://localhost:3001/health >nul 2>&1
    if %errorLevel% equ 0 (
        echo [SUCCESS] Backend is healthy
    ) else (
        echo [ERROR] Backend is not responding
    )

    powershell -command "try { Invoke-WebRequest -Uri 'http://localhost:3000' -TimeoutSec 5 -UseBasicParsing; exit 0 } catch { exit 1 }" >nul 2>&1
    if %errorLevel% equ 0 (
        echo [SUCCESS] Frontend is healthy
    ) else (
        echo [ERROR] Frontend is not responding
    )

    echo.
    echo [INFO] Application URLs:
    echo Frontend: http://localhost:3000
    echo Backend:  http://localhost:3001
    echo.
    pause
) else if "!choice!"=="5" (
    echo [INFO] Goodbye!
    exit /b 0
) else (
    echo [ERROR] Invalid choice
    pause
)

echo.
echo [INFO] Returning to main menu...
pause
goto :eof