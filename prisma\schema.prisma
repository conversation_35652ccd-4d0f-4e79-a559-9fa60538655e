// University Schedule Assistant - Prisma Schema
// This schema corresponds to the Phase 1 database implementation

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// =============================================
// ENUMS
// =============================================

enum RoomType {
  LECTURE_HALL @map("Lecture Hall")
  CLASSROOM    @map("Classroom")
  LAB          @map("Lab")
  TUTORIAL_ROOM @map("Tutorial Room")
  COMPUTER_LAB @map("Computer Lab")

  @@map("room_type_enum")
}

enum GroupType {
  FULL_LEVEL @map("Full Level")
  GROUP      @map("Group")
  SUB_GROUP  @map("Sub-Group")

  @@map("group_type_enum")
}

enum LessonType {
  LECTURE      @map("Lecture")
  LAB          @map("Lab")
  TUTORIAL     @map("Tutorial")
  COMPUTER_LAB @map("Computer Lab")
  SEMINAR      @map("Seminar")
  WORKSHOP     @map("Workshop")

  @@map("lesson_type_enum")
}

enum ScheduleStatus {
  SCHEDULED @map("Scheduled")
  LOCKED    @map("Locked")
  CANCELLED @map("Cancelled")
  MODIFIED  @map("Modified")

  @@map("schedule_status_enum")
}

enum DayOfWeek {
  MONDAY    @map("Monday")
  TUESDAY   @map("Tuesday")
  WEDNESDAY @map("Wednesday")
  THURSDAY  @map("Thursday")
  FRIDAY    @map("Friday")
  SATURDAY  @map("Saturday")
  SUNDAY    @map("Sunday")

  @@map("day_of_week_enum")
}

enum SubstitutionStatus {
  PENDING   @map("Pending")
  APPROVED  @map("Approved")
  REJECTED  @map("Rejected")
  COMPLETED @map("Completed")

  @@map("substitution_status_enum")
}

enum ConstraintType {
  HARD     @map("Hard")
  SOFT     @map("Soft")
  ADVANCED @map("Advanced")

  @@map("constraint_type_enum")
}

enum GenerationStatus {
  NOT_STARTED            @map("NOT_STARTED")
  DATA_INPUT             @map("DATA_INPUT")
  MANIFEST_GENERATED     @map("MANIFEST_GENERATED")
  MANIFEST_VALIDATED     @map("MANIFEST_VALIDATED")
  SCHEDULING_IN_PROGRESS @map("SCHEDULING_IN_PROGRESS")
  SCHEDULING_COMPLETED   @map("SCHEDULING_COMPLETED")
  SCHEDULING_FAILED      @map("SCHEDULING_FAILED")
  PUBLISHED              @map("PUBLISHED")

  @@map("generation_status_enum")
}

enum UserRole {
  ADMIN                  @map("admin")
  SCHEDULER              @map("scheduler")
  DEPARTMENT_COORDINATOR @map("department_coordinator")
  INSTRUCTOR             @map("instructor")
  STUDENT                @map("student")

  @@map("user_role_enum")
}

// =============================================
// CORE ENTITIES
// =============================================

model Semester {
  semesterID String   @id @default(uuid()) @db.Uuid
  name       String   @db.VarChar(100)
  startDate  DateTime @db.Date
  endDate    DateTime @db.Date
  isActive   Boolean  @default(false)
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  lessons            Lesson[]
  scheduledLessons   ScheduledLesson[]
  publishedSchedules PublishedSchedule[]
  generationSessions GenerationSession[]
  constraints        SchedulingConstraint[]

  @@map("Semesters")
}

model Department {
  departmentID String   @id @default(uuid()) @db.Uuid
  name         String   @unique @db.VarChar(200)
  code         String?  @unique @db.VarChar(10)
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  instructors   Instructor[]
  rooms         Room[]
  studentLevels StudentLevel[]
  courses       Course[]
  users         User[]

  @@map("Departments")
}

model Room {
  roomID               String     @id @default(uuid()) @db.Uuid
  roomNumber           String     @unique @db.VarChar(50)
  maxCapacity          Int
  type                 RoomType
  assignedDepartmentID String?    @db.Uuid
  building             String?    @db.VarChar(100)
  floorNumber          Int?       @map("floor_number")
  equipmentNotes       String?    @map("equipment_notes")
  isActive             Boolean    @default(true)
  createdAt            DateTime   @default(now()) @map("created_at")
  updatedAt            DateTime   @updatedAt @map("updated_at")

  // Relations
  assignedDepartment Department? @relation(fields: [assignedDepartmentID], references: [departmentID])
  scheduledLessons   ScheduledLesson[]

  @@map("Rooms")
}

model Instructor {
  instructorID   String   @id @default(uuid()) @db.Uuid
  name           String   @db.VarChar(200)
  email          String?  @unique @db.VarChar(255)
  departmentID   String   @db.Uuid
  maxDailySlots  Int      @default(20)
  isActive       Boolean  @default(true)
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  department               Department               @relation(fields: [departmentID], references: [departmentID])
  availability             InstructorAvailability[]
  lessons                  Lesson[]
  originalSubstitutions    Substitution[]           @relation("OriginalInstructor")
  replacementSubstitutions Substitution[]           @relation("ReplacementInstructor")
  users                    User[]

  @@map("Instructors")
}

model InstructorAvailability {
  availabilityID String   @id @default(uuid()) @db.Uuid
  instructorID   String   @db.Uuid
  dayOfWeek      String   @db.VarChar(10)
  availableSlots Json     @default("[]") @db.JsonB
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  instructor Instructor @relation(fields: [instructorID], references: [instructorID], onDelete: Cascade)

  @@unique([instructorID, dayOfWeek])
  @@map("InstructorAvailability")
}

model StudentLevel {
  levelID              String   @id @default(uuid()) @db.Uuid
  name                 String   @db.VarChar(200)
  departmentID         String   @db.Uuid
  expectedStudentCount Int
  yearNumber           Int?
  semesterNumber       Int?
  createdAt            DateTime @default(now()) @map("created_at")
  updatedAt            DateTime @updatedAt @map("updated_at")

  // Relations
  department    Department     @relation(fields: [departmentID], references: [departmentID])
  studentGroups StudentGroup[]

  @@map("StudentLevels")
}

model StudentGroup {
  groupID       String    @id @default(uuid()) @db.Uuid
  levelID       String    @db.Uuid
  groupName     String    @db.VarChar(100)
  parentGroupID String?   @db.Uuid
  groupType     GroupType
  studentCount  Int
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Relations
  level       StudentLevel   @relation(fields: [levelID], references: [levelID])
  parentGroup StudentGroup?  @relation("GroupHierarchy", fields: [parentGroupID], references: [groupID])
  subGroups   StudentGroup[] @relation("GroupHierarchy")
  lessons     Lesson[]

  @@unique([levelID, groupName])
  @@map("StudentGroups")
}

model Course {
  courseID     String   @id @default(uuid()) @db.Uuid
  courseCode   String   @db.VarChar(20)
  title        String   @db.VarChar(300)
  departmentID String   @db.Uuid
  credits      Int?
  description  String?
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  department Department @relation(fields: [departmentID], references: [departmentID])
  lessons    Lesson[]

  @@unique([courseCode, departmentID])
  @@map("Courses")
}

model Lesson {
  lessonID            String   @id @default(uuid()) @db.Uuid
  courseID            String   @db.Uuid
  studentGroupID      String   @db.Uuid
  instructorID        String   @db.Uuid
  durationInSlots     Int
  lessonType          LessonType
  requiredRoomType    RoomType
  semesterID          String   @db.Uuid
  sessionsPerWeek     Int      @default(1)
  totalSessions       Int?
  priority            Int      @default(5)
  specialRequirements String?
  notes               String?
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  course           Course            @relation(fields: [courseID], references: [courseID])
  studentGroup     StudentGroup      @relation(fields: [studentGroupID], references: [groupID])
  instructor       Instructor        @relation(fields: [instructorID], references: [instructorID])
  semester         Semester          @relation(fields: [semesterID], references: [semesterID])
  scheduledLessons ScheduledLesson[]

  @@map("Lessons")
}

// =============================================
// OUTPUT & OPERATIONAL TABLES
// =============================================

model ScheduledLesson {
  scheduledLessonID String         @id @default(uuid()) @db.Uuid
  lessonID          String         @db.Uuid
  roomID            String         @db.Uuid
  dayOfWeek         DayOfWeek
  startSlot         Int
  endSlot           Int
  semesterID        String         @db.Uuid
  weekNumber        Int            @default(1)
  status            ScheduleStatus @default(SCHEDULED)
  lockedBy          String?        @db.Uuid
  lockedAt          DateTime?
  createdAt         DateTime       @default(now()) @map("created_at")
  updatedAt         DateTime       @updatedAt @map("updated_at")

  // Relations
  lesson       Lesson        @relation(fields: [lessonID], references: [lessonID])
  room         Room          @relation(fields: [roomID], references: [roomID])
  semester     Semester      @relation(fields: [semesterID], references: [semesterID])
  substitutions Substitution[]

  @@unique([roomID, dayOfWeek, startSlot, endSlot, semesterID, weekNumber])
  @@unique([lessonID, dayOfWeek, startSlot, endSlot, semesterID, weekNumber])
  @@map("ScheduledLessons")
}

model Substitution {
  substitutionID          String             @id @default(uuid()) @db.Uuid
  originalInstructorID    String             @db.Uuid
  replacementInstructorID String?            @db.Uuid
  scheduledLessonID       String             @db.Uuid
  dateOfAbsence           DateTime           @db.Date
  reason                  String?
  status                  SubstitutionStatus @default(PENDING)
  notes                   String?
  requestedBy             String?            @db.Uuid
  approvedBy              String?            @db.Uuid
  createdAt               DateTime           @default(now()) @map("created_at")
  updatedAt               DateTime           @updatedAt @map("updated_at")

  // Relations
  originalInstructor    Instructor      @relation("OriginalInstructor", fields: [originalInstructorID], references: [instructorID])
  replacementInstructor Instructor?     @relation("ReplacementInstructor", fields: [replacementInstructorID], references: [instructorID])
  scheduledLesson       ScheduledLesson @relation(fields: [scheduledLessonID], references: [scheduledLessonID])

  @@map("Substitutions")
}

model PublishedSchedule {
  publishedScheduleID String   @id @default(uuid()) @db.Uuid
  semesterID          String   @db.Uuid
  scheduleData        Json     @db.JsonB
  publishedAt         DateTime @default(now())
  publishedBy         String?  @db.Uuid
  version             Int      @default(1)
  isActive            Boolean  @default(true)
  notes               String?

  // Relations
  semester Semester @relation(fields: [semesterID], references: [semesterID])

  @@map("PublishedSchedules")
}

model SchedulingConstraint {
  constraintID   String         @id @default(uuid()) @db.Uuid
  name           String         @db.VarChar(200)
  description    String?
  constraintType ConstraintType
  ruleDefinition Json           @db.JsonB
  weight         Int            @default(1)
  isActive       Boolean        @default(true)
  semesterID     String?        @db.Uuid
  createdAt      DateTime       @default(now()) @map("created_at")
  updatedAt      DateTime       @updatedAt @map("updated_at")

  // Relations
  semester Semester? @relation(fields: [semesterID], references: [semesterID])

  @@map("SchedulingConstraints")
}

model GenerationSession {
  sessionID              String           @id @default(uuid()) @db.Uuid
  semesterID             String           @db.Uuid
  status                 GenerationStatus @default(NOT_STARTED)
  manifestFilePath       String?          @db.VarChar(500)
  jobID                  String?          @db.VarChar(100)
  generationStartedAt    DateTime?
  generationCompletedAt  DateTime?
  totalLessons           Int?
  scheduledLessons       Int?
  unscheduledLessons     Int?
  qualityScore           Decimal?         @db.Decimal(5, 2)
  constraintViolations   Json?            @db.JsonB
  errorLog               String?
  createdBy              String?          @db.Uuid
  createdAt              DateTime         @default(now()) @map("created_at")
  updatedAt              DateTime         @updatedAt @map("updated_at")

  // Relations
  semester Semester @relation(fields: [semesterID], references: [semesterID])

  @@map("GenerationSessions")
}

model User {
  userID       String    @id @default(uuid()) @db.Uuid
  username     String    @unique @db.VarChar(100)
  email        String    @unique @db.VarChar(255)
  passwordHash String    @db.VarChar(255)
  role         UserRole  @default(INSTRUCTOR)
  departmentID String?   @db.Uuid
  instructorID String?   @db.Uuid
  isActive     Boolean   @default(true)
  lastLogin    DateTime?
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  department Department? @relation(fields: [departmentID], references: [departmentID])
  instructor Instructor? @relation(fields: [instructorID], references: [instructorID])

  @@map("Users")
}