@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Docker Storage Fix Script
:: This script fixes Docker storage corruption issues

echo ========================================
echo Docker Storage Fix - University Schedule Assistant
echo ========================================
echo.

:: Set color for better visibility
color 0C

echo [WARNING] Docker storage corruption detected!
echo [INFO] This script will fix Docker storage issues
echo.

echo [INFO] The following steps will be performed:
echo 1. Stop all Docker containers
echo 2. Stop Docker Desktop
echo 3. Clean Docker data
echo 4. Restart Docker Desktop
echo 5. Verify Docker is working
echo.

echo [WARNING] This will remove all Docker containers, images, and volumes!
echo [WARNING] Your application data will be preserved, but <PERSON><PERSON> will need to rebuild everything.
echo.

set /p "confirm=Do you want to continue? (Y/N): "
if /i "!confirm!" neq "Y" (
    echo [INFO] Operation cancelled by user
    pause
    exit /b 0
)

echo.
echo [INFO] Starting Docker storage fix...
echo.

:: Step 1: Stop all containers
echo [INFO] Step 1: Stopping all Docker containers...
docker stop $(docker ps -aq) 2>nul
docker rm $(docker ps -aq) 2>nul
echo [SUCCESS] All containers stopped and removed
echo.

:: Step 2: Stop Docker Desktop
echo [INFO] Step 2: Stopping Docker Desktop...
taskkill /f /im "Docker Desktop.exe" 2>nul
taskkill /f /im "dockerd.exe" 2>nul
taskkill /f /im "com.docker.backend.exe" 2>nul
taskkill /f /im "com.docker.cli.exe" 2>nul

:: Wait for Docker to fully stop
echo [INFO] Waiting for Docker to fully stop...
timeout /t 10 /nobreak >nul
echo [SUCCESS] Docker Desktop stopped
echo.

:: Step 3: Clean Docker data
echo [INFO] Step 3: Cleaning Docker data...
echo [INFO] This may take a few minutes...

:: Clean Docker Desktop data
if exist "%APPDATA%\Docker" (
    echo [INFO] Cleaning Docker Desktop configuration...
    rmdir /s /q "%APPDATA%\Docker" 2>nul
)

if exist "%LOCALAPPDATA%\Docker" (
    echo [INFO] Cleaning Docker Desktop local data...
    rmdir /s /q "%LOCALAPPDATA%\Docker" 2>nul
)

:: Clean WSL Docker data (if using WSL2)
wsl --shutdown 2>nul
echo [INFO] WSL shutdown completed
echo.

:: Step 4: Restart Docker Desktop
echo [INFO] Step 4: Starting Docker Desktop...
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"
echo [INFO] Docker Desktop is starting... (this may take 2-3 minutes)
echo.

:: Wait for Docker to start
echo [INFO] Waiting for Docker to initialize...
set /a "attempts=0"
set /a "max_attempts=60"

:wait_docker_start
set /a "attempts+=1"
if %attempts% gtr %max_attempts% (
    echo [ERROR] Docker failed to start within expected time
    echo [INFO] Please manually start Docker Desktop and try again
    pause
    exit /b 1
)

docker info >nul 2>&1
if %errorLevel% neq 0 (
    echo [INFO] Still waiting for Docker... (attempt %attempts%/%max_attempts%)
    timeout /t 5 /nobreak >nul
    goto wait_docker_start
)

echo [SUCCESS] Docker is now running!
echo.

:: Step 5: Verify Docker is working
echo [INFO] Step 5: Verifying Docker functionality...
docker --version
docker info | findstr "Server Version"
echo.

:: Test basic Docker functionality
echo [INFO] Testing Docker functionality...
docker run --rm hello-world >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Docker is working correctly!
) else (
    echo [WARNING] Docker test failed, but basic functionality appears to work
)
echo.

:: Clean up any test containers
docker system prune -f >nul 2>&1

echo [SUCCESS] Docker storage fix completed!
echo.
echo [INFO] Next steps:
echo 1. Run start-application.bat to rebuild and start your application
echo 2. The first startup will take longer as Docker needs to download images
echo 3. All your application code and data are preserved
echo.

echo [INFO] Docker is now ready for use!
pause