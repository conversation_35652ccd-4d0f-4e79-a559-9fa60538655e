@echo off
setlocal enabledelayedexpansion

:: University Schedule Assistant - Maintenance & Troubleshooting Tools
:: This script combines all maintenance, debugging, and troubleshooting functions

echo ========================================
echo University Schedule Assistant Maintenance
echo ========================================
echo.

:: Set color for better visibility
color 0E

echo [INFO] Maintenance & Troubleshooting Console
echo.

echo Available Tools:
echo 1. System Diagnostics
echo 2. Docker Troubleshooting
echo 3. Performance Testing
echo 4. Security Testing
echo 5. Database Tools
echo 6. Log Analysis
echo 7. Cleanup Tools
echo 8. Reset Application
echo 9. Exit
echo.

set /p "choice=Choose tool (1-9): "

if "!choice!"=="1" goto system_diagnostics
if "!choice!"=="2" goto docker_troubleshooting
if "!choice!"=="3" goto performance_testing
if "!choice!"=="4" goto security_testing
if "!choice!"=="5" goto database_tools
if "!choice!"=="6" goto log_analysis
if "!choice!"=="7" goto cleanup_tools
if "!choice!"=="8" goto reset_application
if "!choice!"=="9" goto exit_maintenance

echo [ERROR] Invalid choice
pause
goto :eof

:system_diagnostics
echo.
echo ========================================
echo System Diagnostics
echo ========================================
echo.

call :run_system_diagnostics
pause
goto :eof

:docker_troubleshooting
echo.
echo ========================================
echo Docker Troubleshooting
echo ========================================
echo.

echo Docker Troubleshooting Options:
echo 1. Quick Docker Fix (System Prune)
echo 2. Complete Docker Reset
echo 3. Docker Storage Analysis
echo 4. Container Rebuild
echo 5. Back to Main Menu
echo.

set /p "docker_choice=Choose option (1-5): "

if "!docker_choice!"=="1" call :quick_docker_fix
if "!docker_choice!"=="2" call :complete_docker_reset
if "!docker_choice!"=="3" call :docker_storage_analysis
if "!docker_choice!"=="4" call :container_rebuild
if "!docker_choice!"=="5" goto :eof

pause
goto :eof

:performance_testing
echo.
echo ========================================
echo Performance Testing
echo ========================================
echo.

call :run_performance_tests
pause
goto :eof

:security_testing
echo.
echo ========================================
echo Security Testing
echo ========================================
echo.

call :run_security_tests
pause
goto :eof

:database_tools
echo.
echo ========================================
echo Database Tools
echo ========================================
echo.

echo Database Tool Options:
echo 1. Database Health Check
echo 2. Reset Database
echo 3. Import Sample Data
echo 4. Database Backup
echo 5. Database Restore
echo 6. Prisma Studio (Web Interface)
echo 7. Back to Main Menu
echo.

set /p "db_choice=Choose option (1-7): "

if "!db_choice!"=="1" call :database_health_check
if "!db_choice!"=="2" call :reset_database
if "!db_choice!"=="3" call :import_sample_data
if "!db_choice!"=="4" call :database_backup
if "!db_choice!"=="5" call :database_restore
if "!db_choice!"=="6" call :open_prisma_studio
if "!db_choice!"=="7" goto :eof

pause
goto :eof

:log_analysis
echo.
echo ========================================
echo Log Analysis
echo ========================================
echo.

call :analyze_logs
pause
goto :eof

:cleanup_tools
echo.
echo ========================================
echo Cleanup Tools
echo ========================================
echo.

echo Cleanup Options:
echo 1. Clean Docker Resources
echo 2. Clean Application Logs
echo 3. Clean Temporary Files
echo 4. Clean Old Backups
echo 5. Complete Cleanup (All)
echo 6. Back to Main Menu
echo.

set /p "cleanup_choice=Choose option (1-6): "

if "!cleanup_choice!"=="1" call :clean_docker_resources
if "!cleanup_choice!"=="2" call :clean_application_logs
if "!cleanup_choice!"=="3" call :clean_temporary_files
if "!cleanup_choice!"=="4" call :clean_old_backups
if "!cleanup_choice!"=="5" call :complete_cleanup
if "!cleanup_choice!"=="6" goto :eof

pause
goto :eof

:reset_application
echo.
echo ========================================
echo Reset Application
echo ========================================
echo.

echo [WARNING] This will completely reset the application!
echo [WARNING] All data, containers, and configurations will be removed!
echo.

set /p "confirm=Are you sure you want to reset everything? (Y/N): "
if /i "!confirm!" neq "Y" (
    echo [INFO] Reset cancelled
    goto :eof
)

call :complete_application_reset
pause
goto :eof

:exit_maintenance
echo.
echo [INFO] Goodbye!
exit /b 0

:: ========================================
:: FUNCTION IMPLEMENTATIONS
:: ========================================

:run_system_diagnostics
echo [INFO] Running comprehensive system diagnostics...
echo.

:: System Information
echo [INFO] System Information:
echo OS: %OS%
echo Processor: %PROCESSOR_IDENTIFIER%
echo Architecture: %PROCESSOR_ARCHITECTURE%
echo.

:: Docker Information
echo [INFO] Docker Information:
docker --version 2>nul
if %errorLevel% neq 0 (
    echo [ERROR] Docker not found
) else (
    docker info | findstr "Server Version\|Storage Driver\|Operating System\|Total Memory\|CPUs"
)
echo.

:: Node.js Information
echo [INFO] Node.js Information:
node --version 2>nul
if %errorLevel% neq 0 (
    echo [ERROR] Node.js not found
) else (
    npm --version
)
echo.

:: Port Usage Check
echo [INFO] Port Usage Check:
netstat -an | findstr ":3000 :3001 :5432 :6379"
echo.

:: Disk Space Check
echo [INFO] Disk Space:
for /f "tokens=3" %%a in ('dir /-c ^| findstr /C:"bytes free"') do set "free_space=%%a"
echo Free Space: %free_space% bytes
echo.

:: File Existence Check
echo [INFO] Required Files Check:
if exist "package.json" (echo [SUCCESS] package.json exists) else (echo [ERROR] package.json missing)
if exist "frontend\package.json" (echo [SUCCESS] frontend\package.json exists) else (echo [ERROR] frontend\package.json missing)
if exist "prisma\schema.prisma" (echo [SUCCESS] prisma\schema.prisma exists) else (echo [ERROR] prisma\schema.prisma missing)
if exist "docker-compose.yml" (echo [SUCCESS] docker-compose.yml exists) else (echo [ERROR] docker-compose.yml missing)
if exist ".env" (echo [SUCCESS] .env exists) else (echo [WARNING] .env missing)
echo.

:: Docker Compose Configuration Check
echo [INFO] Docker Compose Configuration:
docker-compose config >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Docker Compose configuration is valid
) else (
    echo [ERROR] Docker Compose configuration has errors
)
echo.

:: Container Status
echo [INFO] Container Status:
docker-compose ps
echo.

echo [SUCCESS] System diagnostics completed
exit /b 0

:quick_docker_fix
echo [INFO] Performing quick Docker system prune...
docker system prune -a -f --volumes
if %errorLevel% equ 0 (
    echo [SUCCESS] Quick Docker fix completed
    echo [INFO] Try starting the application now
) else (
    echo [ERROR] Quick Docker fix failed
)
exit /b 0

:complete_docker_reset
echo [WARNING] This will completely reset Docker Desktop!
echo [WARNING] All containers, images, and volumes will be removed!
set /p "confirm=Continue? (Y/N): "
if /i "!confirm!" neq "Y" exit /b 0

echo [INFO] Stopping all containers...
docker stop $(docker ps -aq) 2>nul
docker rm $(docker ps -aq) 2>nul

echo [INFO] Removing all images and volumes...
docker system prune -a -f --volumes

echo [INFO] Stopping Docker Desktop...
taskkill /f /im "Docker Desktop.exe" 2>nul
timeout /t 10 /nobreak >nul

echo [INFO] Starting Docker Desktop...
start "" "C:\Program Files\Docker\Docker\Docker Desktop.exe"

echo [INFO] Waiting for Docker to restart...
set /a "attempts=0"
:wait_docker_restart
set /a "attempts+=1"
if %attempts% gtr 60 (
    echo [ERROR] Docker failed to restart
    exit /b 1
)
docker info >nul 2>&1
if %errorLevel% neq 0 (
    timeout /t 5 /nobreak >nul
    goto wait_docker_restart
)

echo [SUCCESS] Docker reset completed
exit /b 0

:docker_storage_analysis
echo [INFO] Analyzing Docker storage usage...
echo.

echo [INFO] Docker System Information:
docker system df
echo.

echo [INFO] Container Storage:
docker ps -s
echo.

echo [INFO] Image Storage:
docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}"
echo.

echo [INFO] Volume Storage:
docker volume ls
echo.

echo [INFO] Network Information:
docker network ls
echo.

exit /b 0

:container_rebuild
echo [INFO] Rebuilding all containers...
docker-compose down
docker-compose build --no-cache
docker-compose up -d
echo [SUCCESS] Container rebuild completed
exit /b 0

:database_health_check
echo [INFO] Performing comprehensive database health check...
echo.

:: Check if database container is running
docker-compose ps postgres | findstr "Up" >nul 2>&1
if %errorLevel% neq 0 (
    echo [ERROR] Database container is not running
    exit /b 1
)
echo [SUCCESS] Database container is running

:: Check database connection
docker-compose exec -T postgres pg_isready -U schedule_user -d schedule_assistant >nul 2>&1
if %errorLevel% equ 0 (
    echo [SUCCESS] Database connection is healthy
) else (
    echo [ERROR] Database connection failed
    exit /b 1
)

:: Check database size
echo [INFO] Database size information:
docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -c "SELECT pg_size_pretty(pg_database_size('schedule_assistant')) as database_size;"

:: Check table information
echo [INFO] Table information:
docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -c "SELECT schemaname,tablename,attname,typename,char_maximum_length FROM pg_tables t JOIN pg_attribute a ON a.attrelid = t.tablename::regclass JOIN pg_type y ON y.oid = a.atttypid WHERE schemaname = 'public' AND a.attnum > 0 ORDER BY tablename,attname;"

:: Check for data
echo [INFO] Data summary:
docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -c "SELECT 'Users' as table_name, COUNT(*) as record_count FROM \"User\" UNION ALL SELECT 'Departments', COUNT(*) FROM \"Department\" UNION ALL SELECT 'Courses', COUNT(*) FROM \"Course\" UNION ALL SELECT 'Rooms', COUNT(*) FROM \"Room\";" 2>nul

echo [SUCCESS] Database health check completed
exit /b 0

:reset_database
echo [WARNING] This will completely reset the database!
echo [WARNING] All data will be lost!
set /p "confirm=Continue? (Y/N): "
if /i "!confirm!" neq "Y" exit /b 0

echo [INFO] Stopping database container...
docker-compose stop postgres

echo [INFO] Removing database volume...
docker volume rm schedule_assisstnant_postgres_data 2>nul

echo [INFO] Starting database container...
docker-compose up -d postgres

echo [INFO] Waiting for database to be ready...
timeout /t 15 /nobreak >nul

echo [INFO] Running database migrations...
docker-compose exec -T api npx prisma migrate deploy

echo [SUCCESS] Database reset completed
exit /b 0

:import_sample_data
echo [INFO] Importing sample data...

:: Check if test data generator exists
if exist "testing\generate-test-data.js" (
    echo [INFO] Running test data generator...
    cd testing
    node generate-test-data.js
    cd ..
    echo [SUCCESS] Sample data imported
) else (
    echo [WARNING] Test data generator not found
    echo [INFO] Creating basic sample data...

    :: Create basic sample data using SQL
    docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant -c "
    INSERT INTO \"Department\" (id, name, code, description) VALUES
    ('dept1', 'Computer Science', 'CS', 'Computer Science Department'),
    ('dept2', 'Mathematics', 'MATH', 'Mathematics Department')
    ON CONFLICT (id) DO NOTHING;

    INSERT INTO \"Room\" (id, name, capacity, type, building, floor) VALUES
    ('room1', 'CS Lab 1', 30, 'LAB', 'Science Building', 1),
    ('room2', 'Lecture Hall A', 100, 'LECTURE', 'Main Building', 2)
    ON CONFLICT (id) DO NOTHING;
    "
    echo [SUCCESS] Basic sample data created
)
exit /b 0

:database_backup
echo [INFO] Creating database backup...
set "TIMESTAMP=%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "TIMESTAMP=%TIMESTAMP: =0%"
set "BACKUP_FILE=backups\database_backup_%TIMESTAMP%.sql"

if not exist "backups" mkdir backups

docker-compose exec -T postgres pg_dump -U schedule_user schedule_assistant > "%BACKUP_FILE%"
if %errorLevel% equ 0 (
    echo [SUCCESS] Database backup created: %BACKUP_FILE%
) else (
    echo [ERROR] Database backup failed
)
exit /b 0

:database_restore
echo [INFO] Available database backups:
dir backups\database_backup_*.sql /b 2>nul
if %errorLevel% neq 0 (
    echo [ERROR] No database backups found
    exit /b 1
)

echo.
set /p "backup_file=Enter backup filename (or full path): "

if not exist "%backup_file%" (
    if not exist "backups\%backup_file%" (
        echo [ERROR] Backup file not found
        exit /b 1
    )
    set "backup_file=backups\%backup_file%"
)

echo [WARNING] This will replace all current database data!
set /p "confirm=Continue? (Y/N): "
if /i "!confirm!" neq "Y" exit /b 0

echo [INFO] Restoring database from %backup_file%...
docker-compose exec -T postgres psql -U schedule_user -d schedule_assistant < "%backup_file%"
if %errorLevel% equ 0 (
    echo [SUCCESS] Database restored successfully
) else (
    echo [ERROR] Database restore failed
)
exit /b 0

:open_prisma_studio
echo [INFO] Opening Prisma Studio...
echo [INFO] Prisma Studio will open in your browser at http://localhost:5555
echo [INFO] Press Ctrl+C to stop Prisma Studio
docker-compose exec api npx prisma studio
exit /b 0

:run_performance_tests
echo [INFO] Running performance tests...

if exist "testing\performance-tests.js" (
    echo [INFO] Running automated performance tests...
    cd testing
    node performance-tests.js
    cd ..
) else (
    echo [INFO] Running basic performance checks...

    :: Check response times
    echo [INFO] Testing backend response time...
    powershell -command "Measure-Command { Invoke-WebRequest -Uri 'http://localhost:3001/health' -UseBasicParsing }" 2>nul

    echo [INFO] Testing frontend response time...
    powershell -command "Measure-Command { Invoke-WebRequest -Uri 'http://localhost:3000' -UseBasicParsing }" 2>nul

    :: Check resource usage
    echo [INFO] Current resource usage:
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
)

echo [SUCCESS] Performance tests completed
exit /b 0

:run_security_tests
echo [INFO] Running security tests...

if exist "testing\security-tests.js" (
    echo [INFO] Running automated security tests...
    cd testing
    node security-tests.js
    cd ..
) else (
    echo [INFO] Running basic security checks...

    :: Check for exposed ports
    echo [INFO] Checking exposed ports...
    netstat -an | findstr ":3000 :3001 :5432 :6379"

    :: Check environment file security
    echo [INFO] Checking .env file security...
    if exist ".env" (
        findstr /i "password\|secret\|key" .env | findstr /v "your_" >nul
        if %errorLevel% equ 0 (
            echo [WARNING] Default passwords/secrets detected in .env file
        ) else (
            echo [SUCCESS] No default passwords found
        )
    )

    :: Check Docker security
    echo [INFO] Checking Docker container security...
    docker-compose exec -T api whoami 2>nul
    docker-compose exec -T frontend whoami 2>nul
)

echo [SUCCESS] Security tests completed
exit /b 0

:analyze_logs
echo [INFO] Analyzing application logs...

:: Check application logs
if exist "logs" (
    echo [INFO] Application log files:
    dir logs /b
    echo.

    echo [INFO] Recent application errors:
    findstr /i "error\|exception\|fail" logs\*.log 2>nul | tail -10
) else (
    echo [INFO] No application logs directory found
)

:: Check Docker logs
echo [INFO] Recent Docker container logs:
echo.
echo [Backend API Logs - Last 10 lines:]
docker-compose logs --tail=10 api 2>nul
echo.
echo [Frontend Logs - Last 10 lines:]
docker-compose logs --tail=10 frontend 2>nul
echo.
echo [Database Logs - Last 10 lines:]
docker-compose logs --tail=10 postgres 2>nul

echo [SUCCESS] Log analysis completed
exit /b 0

:clean_docker_resources
echo [INFO] Cleaning Docker resources...
docker system prune -f
docker volume prune -f
docker network prune -f
echo [SUCCESS] Docker resources cleaned
exit /b 0

:clean_application_logs
echo [INFO] Cleaning application logs...
if exist "logs" (
    del /q logs\*.log 2>nul
    echo [SUCCESS] Application logs cleaned
) else (
    echo [INFO] No logs directory found
)
exit /b 0

:clean_temporary_files
echo [INFO] Cleaning temporary files...
if exist "node_modules\.cache" rmdir /s /q node_modules\.cache 2>nul
if exist "frontend\node_modules\.cache" rmdir /s /q frontend\node_modules\.cache 2>nul
if exist "*.tmp" del /q *.tmp 2>nul
if exist "*.temp" del /q *.temp 2>nul
echo [SUCCESS] Temporary files cleaned
exit /b 0

:clean_old_backups
echo [INFO] Cleaning old backups (keeping last 5)...
if exist "backups" (
    set /a "count=0"
    for /f "skip=5 delims=" %%i in ('dir "backups\backup_*" /b /o-d 2^>nul') do (
        set /a "count+=1"
        rmdir /s /q "backups\%%i" 2>nul
        del /q "backups\%%i" 2>nul
    )
    echo [SUCCESS] Removed !count! old backup(s)
) else (
    echo [INFO] No backups directory found
)
exit /b 0

:complete_cleanup
echo [INFO] Performing complete cleanup...
call :clean_docker_resources
call :clean_application_logs
call :clean_temporary_files
call :clean_old_backups
echo [SUCCESS] Complete cleanup finished
exit /b 0

:complete_application_reset
echo [INFO] Performing complete application reset...

:: Stop all services
echo [INFO] Stopping all services...
docker-compose down

:: Remove all volumes
echo [INFO] Removing all volumes...
docker volume rm schedule_assisstnant_postgres_data 2>nul
docker volume rm schedule_assisstnant_redis_data 2>nul

:: Clean Docker resources
echo [INFO] Cleaning Docker resources...
docker system prune -a -f --volumes

:: Remove application data
echo [INFO] Removing application data...
if exist "logs" rmdir /s /q logs
if exist "uploads" rmdir /s /q uploads
if exist "backups" rmdir /s /q backups

:: Remove node_modules
echo [INFO] Removing node_modules...
if exist "node_modules" rmdir /s /q node_modules
if exist "frontend\node_modules" rmdir /s /q frontend\node_modules

:: Remove .env file
echo [INFO] Removing .env file...
if exist ".env" del /q .env

echo [SUCCESS] Complete application reset finished
echo [INFO] Run app-manager.bat to set up the application again
exit /b 0