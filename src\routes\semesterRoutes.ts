import { Router } from 'express';
import { <PERSON><PERSON>ter<PERSON>ontroller } from '../controllers/semesterController';
import { authenticateToken, requireScheduler } from '../middleware/auth';

const router = Router();

/**
 * Semester Routes
 * All routes require authentication
 * Create, update, delete, and activate operations require scheduler role
 */

// GET /api/semesters - Get all semesters
router.get('/', authenticateToken, SemesterController.getAllSemesters);

// GET /api/semesters/active - Get active semester (must be before /:semesterId)
router.get('/active', authenticateToken, SemesterController.getActiveSemester);

// GET /api/semesters/:semesterId - Get semester by ID
router.get('/:semesterId', authenticateToken, SemesterController.getSemesterById);

// POST /api/semesters - Create new semester (requires scheduler role)
router.post('/', authenticateToken, requireScheduler, SemesterController.createSemester);

// PUT /api/semesters/:semesterId - Update semester (requires scheduler role)
router.put('/:semesterId', authenticateToken, requireScheduler, SemesterController.updateSemester);

// DELETE /api/semesters/:semesterId - Delete semester (requires scheduler role)
router.delete('/:semesterId', authenticateToken, requireScheduler, SemesterController.deleteSemester);

// POST /api/semesters/:semesterId/activate - Activate semester (requires scheduler role)
router.post('/:semesterId/activate', authenticateToken, requireScheduler, SemesterController.activateSemester);

export default router;