import express, { Application, Request, Response, NextFunction } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { env, isDevelopment } from './config/env';
import { logger } from './config/logger';
import { checkDatabaseConnection, disconnectDatabase } from './config/database';
import { sendInternalServerError, sendNotFound } from './utils/response';

// Import routes
import semesterRoutes from './routes/semesterRoutes';
import departmentRoutes from './routes/departmentRoutes';
import schedulingRoutes from './routes/scheduling';
import excelRoutes from './routes/excel';

/**
 * University Schedule Assistant - Express Server
 */
class Server {
  private app: Application;
  private port: number;

  constructor() {
    this.app = express();
    this.port = env.PORT;
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  /**
   * Initialize middleware
   */
  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: isDevelopment() ? false : undefined,
    }));

    // CORS configuration
    const allowedOrigins = env.ALLOWED_ORIGINS.split(',').map(origin => origin.trim());
    this.app.use(cors({
      origin: allowedOrigins,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
    }));

    // Compression
    this.app.use(compression());

    // Rate limiting
    const limiter = rateLimit({
      windowMs: env.RATE_LIMIT_WINDOW_MS,
      max: env.RATE_LIMIT_MAX_REQUESTS,
      message: {
        success: false,
        message: 'Too many requests from this IP, please try again later.',
      },
      standardHeaders: true,
      legacyHeaders: false,
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      logger.http(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        body: req.method !== 'GET' ? req.body : undefined,
      });
      next();
    });
  }

  /**
   * Initialize routes
   */
  private initializeRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req: Request, res: Response) => {
      res.status(200).json({
        success: true,
        message: 'Server is healthy',
        timestamp: new Date().toISOString(),
        environment: env.NODE_ENV,
      });
    });

    // API routes
    this.app.use('/api/semesters', semesterRoutes);
    this.app.use('/api/departments', departmentRoutes);
    this.app.use('/api/scheduling', schedulingRoutes);
    this.app.use('/api/excel', excelRoutes);

    // API documentation (development only)
    if (isDevelopment() && env.ENABLE_SWAGGER) {
      this.app.get('/api/docs', (req: Request, res: Response) => {
        res.json({
          message: 'API Documentation',
          endpoints: {
            semesters: '/api/semesters',
            departments: '/api/departments',
            scheduling: '/api/scheduling',
            excel: '/api/excel',
            health: '/health',
          },
          documentation: 'See api_design.md for complete API specification',
        });
      });
    }

    // 404 handler for API routes
    this.app.use('/api/*', (req: Request, res: Response) => {
      sendNotFound(res, `API endpoint ${req.path} not found`);
    });

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        success: true,
        message: 'University Schedule Assistant API',
        version: '1.0.0',
        documentation: isDevelopment() ? '/api/docs' : 'Contact administrator',
      });
    });
  }

  /**
   * Initialize error handling
   */
  private initializeErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      logger.error('Unhandled error', {
        error: error.message,
        stack: error.stack,
        path: req.path,
        method: req.method,
        ip: req.ip,
      });

      sendInternalServerError(res, 'An unexpected error occurred');
    });

    // 404 handler for all other routes
    this.app.use('*', (req: Request, res: Response) => {
      sendNotFound(res, `Route ${req.path} not found`);
    });
  }

  /**
   * Start the server
   */
  public async start(): Promise<void> {
    try {
      // Check database connection
      const dbConnected = await checkDatabaseConnection();
      if (!dbConnected) {
        throw new Error('Failed to connect to database');
      }

      // Start server
      this.app.listen(this.port, () => {
        logger.info(`🚀 Server started successfully`, {
          port: this.port,
          environment: env.NODE_ENV,
          timestamp: new Date().toISOString(),
        });

        if (isDevelopment()) {
          logger.info('📚 Development endpoints:', {
            api: `http://localhost:${this.port}/api`,
            docs: `http://localhost:${this.port}/api/docs`,
            health: `http://localhost:${this.port}/health`,
          });
        }
      });

      // Graceful shutdown handlers
      this.setupGracefulShutdown();

    } catch (error) {
      logger.error('Failed to start server', { error });
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown
   */
  private setupGracefulShutdown(): void {
    const gracefulShutdown = async (signal: string) => {
      logger.info(`Received ${signal}, starting graceful shutdown...`);

      try {
        // Close database connections
        await disconnectDatabase();

        logger.info('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('Error during graceful shutdown', { error });
        process.exit(1);
      }
    };

    // Listen for termination signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception', { error });
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection', { reason, promise });
      process.exit(1);
    });
  }
}

// Start the server
const server = new Server();
server.start().catch((error) => {
  logger.error('Failed to start application', { error });
  process.exit(1);
});

export default server;