# University Schedule Assistant - System Architecture

## Overview

The University Schedule Assistant is designed as a modern, scalable web application using a microservices architecture with clear separation of concerns. The system is built to handle complex scheduling constraints while providing an intuitive user experience.

## Technology Stack

### Backend
- **Runtime**: Node.js 18+ with TypeScript
- **Framework**: Express.js with Helmet, CORS, and compression middleware
- **Database**: PostgreSQL 15+ (primary), Redis (caching & sessions)
- **ORM**: Prisma (type-safe database access)
- **Authentication**: JWT with refresh tokens, bcrypt for password hashing
- **File Processing**: ExcelJS for Excel import/export
- **PDF Generation**: Puppeteer for report generation
- **Real-time**: Socket.IO for live updates
- **Queue System**: Bull Queue with Redis for background jobs
- **Validation**: Zod for runtime type validation
- **Testing**: Jest with Supertest for API testing

### Frontend
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **State Management**: <PERSON>ustand with persistence
- **UI Library**: Material-UI (MUI) v5
- **Data Fetching**: TanStack Query (React Query)
- **Forms**: React Hook Form with Zod validation
- **Routing**: React Router v6
- **Charts**: Recharts for analytics
- **Calendar**: FullCalendar for schedule visualization
- **Drag & Drop**: React DnD for schedule manipulation
- **Excel**: SheetJS for client-side Excel processing

### Infrastructure
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Docker Compose (development), Kubernetes (production)
- **Reverse Proxy**: Nginx
- **Monitoring**: Prometheus + Grafana
- **Logging**: Winston (backend), structured JSON logs
- **CI/CD**: GitHub Actions
- **Cloud**: AWS (RDS, ElastiCache, S3, ECS)

## System Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                          CLIENT LAYER                           │
├─────────────────────────────────────────────────────────────────┤
│  React Frontend (Vite)  │  Mobile App (Future)  │  Excel Files  │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                        API GATEWAY                              │
├─────────────────────────────────────────────────────────────────┤
│              Nginx (Load Balancer, SSL, Rate Limiting)          │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                      APPLICATION LAYER                          │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Auth      │  │   Core      │  │ Scheduling  │  │ Reports │ │
│  │  Service    │  │  Service    │  │   Engine    │  │ Service │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │Substitution │  │   Excel     │  │ Notification│  │WebSocket│ │
│  │  Service    │  │  Service    │  │   Service   │  │ Service │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                       DATA LAYER                                │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │ PostgreSQL  │  │    Redis    │  │   File      │  │  Queue  │ │
│  │ (Primary)   │  │  (Cache)    │  │  Storage    │  │ System  │ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

## Core Modules

### 1. Authentication Service
**Responsibilities:**
- User authentication and authorization
- JWT token management (access + refresh tokens)
- Role-based access control (RBAC)
- Session management
- Password reset functionality

**Key Components:**
- `AuthController`: Login, logout, token refresh
- `AuthMiddleware`: JWT validation, role checking
- `UserService`: User management operations
- `PasswordService`: Secure password handling

### 2. Core Data Service
**Responsibilities:**
- CRUD operations for all core entities
- Data validation and integrity
- Relationship management
- Bulk operations

**Key Components:**
- `DepartmentService`: Department management
- `InstructorService`: Instructor operations
- `RoomService`: Room management
- `CourseService`: Course operations
- `StudentService`: Student level and group management
- `LessonService`: Lesson management

### 3. Scheduling Engine
**Responsibilities:**
- Automatic schedule generation
- Constraint validation and enforcement
- Conflict detection and resolution
- Schedule optimization algorithms
- Manual schedule adjustments

**Key Components:**
- `SchedulingEngine`: Core scheduling algorithm
- `ConstraintValidator`: Hard and soft constraint checking
- `ConflictDetector`: Real-time conflict detection
- `OptimizationService`: Schedule quality improvement
- `ScheduleService`: Schedule CRUD operations

**Algorithm Overview:**
```
1. Data Preparation Phase
   - Load all lessons to be scheduled
   - Load all constraints
   - Build availability matrices

2. Initial Placement Phase
   - Sort lessons by priority and constraints
   - Use constraint satisfaction techniques
   - Apply greedy algorithm with backtracking

3. Optimization Phase
   - Apply local search algorithms
   - Minimize soft constraint violations
   - Improve schedule quality metrics

4. Validation Phase
   - Final constraint validation
   - Generate conflict reports
   - Calculate quality scores
```

### 4. Excel Integration Service
**Responsibilities:**
- Excel file import/export
- Data validation and transformation
- Manifest generation and processing
- Bulk data operations

**Key Components:**
- `ExcelImporter`: Parse and validate Excel files
- `ExcelExporter`: Generate Excel reports
- `ManifestGenerator`: Create scheduling manifests
- `DataTransformer`: Convert between formats

### 5. Substitution Management Service
**Responsibilities:**
- Daily substitution handling
- Substitute instructor suggestions
- Approval workflows
- Bulletin generation

**Key Components:**
- `SubstitutionService`: Core substitution logic
- `SubstituteFinderService`: Find available substitutes
- `BulletinService`: Generate daily bulletins
- `NotificationService`: Send substitution alerts

### 6. Reporting Service
**Responsibilities:**
- Schedule report generation
- Analytics and metrics
- Custom report creation
- PDF generation

**Key Components:**
- `ReportGenerator`: Create various report types
- `AnalyticsService`: Calculate metrics and statistics
- `PDFService`: Generate PDF documents
- `ChartService`: Create charts and visualizations

### 7. Notification Service
**Responsibilities:**
- Real-time notifications
- Email notifications
- System alerts
- User preferences

**Key Components:**
- `NotificationService`: Core notification logic
- `EmailService`: Email delivery
- `WebSocketService`: Real-time updates
- `PreferenceService`: User notification settings

### 8. WebSocket Service
**Responsibilities:**
- Real-time communication
- Live schedule updates
- Generation progress updates
- Collaborative editing support

**Key Components:**
- `WebSocketServer`: Socket.IO server setup
- `EventEmitter`: Broadcast system events
- `RoomManager`: Manage WebSocket rooms
- `MessageHandler`: Process incoming messages

## Data Flow Architecture

### 1. User Data Input Flow
```
User Input → Frontend Validation → API Request → Backend Validation →
Database Transaction → Cache Update → Response → UI Update
```

### 2. Schedule Generation Flow
```
Trigger Generation → Load Data → Validate Constraints →
Run Algorithm → Store Results → Notify Users → Update UI
```

### 3. Real-time Update Flow
```
Data Change → Event Emission → WebSocket Broadcast →
Client Update → UI Refresh
```

## Security Architecture

### Authentication & Authorization
- **JWT Tokens**: Stateless authentication with short-lived access tokens
- **Refresh Tokens**: Secure token renewal mechanism
- **Role-Based Access Control**: Granular permissions based on user roles
- **API Rate Limiting**: Prevent abuse and ensure fair usage
- **Input Validation**: Comprehensive validation at all entry points

### Data Security
- **Encryption at Rest**: Database encryption for sensitive data
- **Encryption in Transit**: HTTPS/TLS for all communications
- **SQL Injection Prevention**: Parameterized queries via ORM
- **XSS Protection**: Content Security Policy and input sanitization
- **CSRF Protection**: Token-based CSRF prevention

### Infrastructure Security
- **Container Security**: Minimal base images, security scanning
- **Network Security**: VPC, security groups, firewall rules
- **Secrets Management**: Environment variables, AWS Secrets Manager
- **Audit Logging**: Comprehensive logging of all actions
- **Backup Security**: Encrypted backups with access controls

## Performance Architecture

### Caching Strategy
- **Redis Cache**: Session data, frequently accessed data
- **Application Cache**: In-memory caching for static data
- **Database Query Optimization**: Proper indexing and query optimization
- **CDN**: Static asset delivery via CloudFront

### Scalability Design
- **Horizontal Scaling**: Load balancer with multiple app instances
- **Database Scaling**: Read replicas for read-heavy operations
- **Queue System**: Background job processing for heavy operations
- **Microservices**: Independent scaling of different services

### Monitoring & Observability
- **Application Metrics**: Response times, error rates, throughput
- **Infrastructure Metrics**: CPU, memory, disk, network usage
- **Business Metrics**: Schedule generation success rates, user activity
- **Alerting**: Automated alerts for critical issues
- **Distributed Tracing**: Request tracing across services

## Deployment Architecture

### Development Environment
```
Docker Compose Setup:
- PostgreSQL container
- Redis container
- Backend API container
- Frontend development server
- Nginx proxy container
```

### Production Environment
```
AWS ECS Cluster:
- Application Load Balancer
- ECS Services (Auto Scaling)
- RDS PostgreSQL (Multi-AZ)
- ElastiCache Redis (Cluster Mode)
- S3 for file storage
- CloudWatch for monitoring
```

### CI/CD Pipeline
```
GitHub → GitHub Actions → Build & Test →
Docker Build → Push to ECR → Deploy to ECS →
Health Checks → Rollback if needed
```

## Error Handling & Resilience

### Error Handling Strategy
- **Graceful Degradation**: System continues to function with reduced features
- **Circuit Breaker Pattern**: Prevent cascading failures
- **Retry Logic**: Automatic retry for transient failures
- **Fallback Mechanisms**: Alternative paths when primary systems fail

### Data Consistency
- **Database Transactions**: ACID compliance for critical operations
- **Event Sourcing**: For audit trails and data recovery
- **Eventual Consistency**: For non-critical real-time updates
- **Backup & Recovery**: Regular automated backups with point-in-time recovery

## Integration Points

### External Systems
- **Email Service**: SMTP/SendGrid for notifications
- **File Storage**: AWS S3 for document storage
- **Authentication**: Optional LDAP/Active Directory integration
- **Calendar Systems**: iCal export for external calendar integration

### API Integration
- **RESTful APIs**: Standard HTTP methods and status codes
- **WebSocket APIs**: Real-time bidirectional communication
- **Webhook Support**: For external system notifications
- **API Versioning**: Backward compatibility maintenance

## Development Guidelines

### Code Organization
```
src/
├── controllers/     # API route handlers
├── services/        # Business logic
├── models/          # Data models and schemas
├── middleware/      # Express middleware
├── utils/           # Utility functions
├── config/          # Configuration files
├── tests/           # Test files
└── types/           # TypeScript type definitions
```

### Testing Strategy
- **Unit Tests**: Individual function and component testing
- **Integration Tests**: API endpoint testing
- **End-to-End Tests**: Full user workflow testing
- **Performance Tests**: Load and stress testing
- **Security Tests**: Vulnerability scanning

### Quality Assurance
- **Code Reviews**: Mandatory peer reviews
- **Static Analysis**: ESLint, Prettier, SonarQube
- **Type Safety**: Full TypeScript coverage
- **Documentation**: Comprehensive API and code documentation
- **Continuous Integration**: Automated testing on every commit