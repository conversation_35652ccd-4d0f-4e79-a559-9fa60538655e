{"version": 3, "file": "BrowserContext.js", "sourceRoot": "", "sources": ["../../../../src/bidi/BrowserContext.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAKH,OAAO,EAAC,cAAc,EAAC,MAAM,0BAA0B,CAAC;AAGxD,OAAO,EAAC,oBAAoB,EAAC,MAAM,qBAAqB,CAAC;AACzD,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAK7C,OAAO,EAAC,WAAW,EAAC,MAAM,uBAAuB,CAAC;AAUlD;;GAEG;AACH,MAAM,OAAO,kBAAmB,SAAQ,cAAc;IACpD,QAAQ,CAAc;IACtB,WAAW,CAAiB;IAC5B,gBAAgB,CAAkB;IAClC,YAAY,CAAc;IAE1B,YACE,OAAoB,EACpB,WAAwB,EACxB,OAAkC;QAElC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QACxB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;QAC5C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC;IAClD,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7C,OAAO,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC;QAC1C,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,aAAa,CACpB,SAAoD,EACpD,UAAgC,EAAE;QAElC,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;YAC1C,OAAO,MAAM,CAAC,cAAc,EAAE,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,CAAC,CAAC;QAC/D,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEQ,KAAK,CAAC,OAAO;QACpB,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACrE,IAAI,iDAAqC;YACzC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE;SAClC,CAAC,CAAC;QACH,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAE5D,sEAAsE;QACtE,kEAAkE;QAClE,2EAA2E;QAC3E,yEAAyE;QACzE,6CAA6C;QAC7C,MAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;QAEhC,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;QACjC,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;QACvC,CAAC;QACD,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAAC,MAAM,CAAC;gBACP,yCAAyC;YAC3C,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;QACvD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;IACH,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,KAAK,CAAC,KAAK;QAClB,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAC/B,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YAC1B,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC;QAClB,CAAC,CAAC,CACH,CAAC;QACF,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAiB,EAAE;YACzC,OAAO,CAAC,KAAK,IAAI,CAAC;QACpB,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,WAAW;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,WAAW,CAAC,OAAO,CAAC;IACtD,CAAC;IAEQ,mBAAmB;QAC1B,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IAEQ,wBAAwB;QAC/B,MAAM,IAAI,oBAAoB,EAAE,CAAC;IACnC,CAAC;IAED,IAAa,EAAE;QACb,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,KAAK,SAAS,EAAE,CAAC;YACvC,OAAO,SAAS,CAAC;QACnB,CAAC;QACD,OAAO,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC;IAC9B,CAAC;CACF"}