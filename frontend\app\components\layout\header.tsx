import { Link } from "react-router";
import { Calendar, Users, Building, BookOpen, Settings, LogOut } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";

export function Header() {
  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center">
        {/* Logo and Title */}
        <div className="flex items-center space-x-4">
          <Calendar className="h-8 w-8 text-primary" />
          <div>
            <h1 className="text-xl font-bold">Schedule Assistant</h1>
            <p className="text-sm text-muted-foreground">University Scheduling System</p>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex items-center space-x-6 ml-8">
          <Link
            to="/dashboard"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Dashboard
          </Link>
          <Link
            to="/semesters"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Semesters
          </Link>
          <Link
            to="/departments"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Departments
          </Link>
          <Link
            to="/rooms"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Rooms
          </Link>
          <Link
            to="/instructors"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Instructors
          </Link>
          <Link
            to="/courses"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Courses
          </Link>
          <Link
            to="/schedule"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Schedule
          </Link>
          <Link
            to="/excel"
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Excel
          </Link>
        </nav>

        {/* User Actions */}
        <div className="ml-auto flex items-center space-x-4">
          <Button variant="ghost" size="icon">
            <Settings className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <LogOut className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </header>
  );
}