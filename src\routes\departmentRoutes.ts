import { Router } from 'express';
import { DepartmentController } from '../controllers/departmentController';
import { authenticateToken, requireScheduler, requireDepartmentAccess } from '../middleware/auth';

const router = Router();

/**
 * Department Routes
 * All routes require authentication
 * Create, update, and delete operations require scheduler role
 * Some read operations require department access control
 */

// GET /api/departments/dropdown - Get departments for dropdown (must be before /:departmentId)
router.get('/dropdown', authenticateToken, DepartmentController.getDepartmentsForDropdown);

// GET /api/departments - Get all departments
router.get('/', authenticateToken, DepartmentController.getAllDepartments);

// GET /api/departments/:departmentId - Get department by ID
router.get('/:departmentId', authenticateToken, DepartmentController.getDepartmentById);

// GET /api/departments/:departmentId/statistics - Get department statistics
router.get('/:departmentId/statistics', authenticateToken, requireDepartmentAccess, DepartmentController.getDepartmentStatistics);

// POST /api/departments - Create new department (requires scheduler role)
router.post('/', authenticateToken, requireScheduler, DepartmentController.createDepartment);

// PUT /api/departments/:departmentId - Update department (requires scheduler role)
router.put('/:departmentId', authenticateToken, requireScheduler, DepartmentController.updateDepartment);

// DELETE /api/departments/:departmentId - Delete department (requires scheduler role)
router.delete('/:departmentId', authenticateToken, requireScheduler, DepartmentController.deleteDepartment);

export default router;