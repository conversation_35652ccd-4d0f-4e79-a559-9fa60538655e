
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.SemesterScalarFieldEnum = {
  semesterID: 'semesterID',
  name: 'name',
  startDate: 'startDate',
  endDate: 'endDate',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.DepartmentScalarFieldEnum = {
  departmentID: 'departmentID',
  name: 'name',
  code: 'code',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.RoomScalarFieldEnum = {
  roomID: 'roomID',
  roomNumber: 'roomNumber',
  maxCapacity: 'maxCapacity',
  type: 'type',
  assignedDepartmentID: 'assignedDepartmentID',
  building: 'building',
  floorNumber: 'floorNumber',
  equipmentNotes: 'equipmentNotes',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InstructorScalarFieldEnum = {
  instructorID: 'instructorID',
  name: 'name',
  email: 'email',
  departmentID: 'departmentID',
  maxDailySlots: 'maxDailySlots',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.InstructorAvailabilityScalarFieldEnum = {
  availabilityID: 'availabilityID',
  instructorID: 'instructorID',
  dayOfWeek: 'dayOfWeek',
  availableSlots: 'availableSlots',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentLevelScalarFieldEnum = {
  levelID: 'levelID',
  name: 'name',
  departmentID: 'departmentID',
  expectedStudentCount: 'expectedStudentCount',
  yearNumber: 'yearNumber',
  semesterNumber: 'semesterNumber',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.StudentGroupScalarFieldEnum = {
  groupID: 'groupID',
  levelID: 'levelID',
  groupName: 'groupName',
  parentGroupID: 'parentGroupID',
  groupType: 'groupType',
  studentCount: 'studentCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CourseScalarFieldEnum = {
  courseID: 'courseID',
  courseCode: 'courseCode',
  title: 'title',
  departmentID: 'departmentID',
  credits: 'credits',
  description: 'description',
  isActive: 'isActive',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.LessonScalarFieldEnum = {
  lessonID: 'lessonID',
  courseID: 'courseID',
  studentGroupID: 'studentGroupID',
  instructorID: 'instructorID',
  durationInSlots: 'durationInSlots',
  lessonType: 'lessonType',
  requiredRoomType: 'requiredRoomType',
  semesterID: 'semesterID',
  sessionsPerWeek: 'sessionsPerWeek',
  totalSessions: 'totalSessions',
  priority: 'priority',
  specialRequirements: 'specialRequirements',
  notes: 'notes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ScheduledLessonScalarFieldEnum = {
  scheduledLessonID: 'scheduledLessonID',
  lessonID: 'lessonID',
  roomID: 'roomID',
  dayOfWeek: 'dayOfWeek',
  startSlot: 'startSlot',
  endSlot: 'endSlot',
  semesterID: 'semesterID',
  weekNumber: 'weekNumber',
  status: 'status',
  lockedBy: 'lockedBy',
  lockedAt: 'lockedAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SubstitutionScalarFieldEnum = {
  substitutionID: 'substitutionID',
  originalInstructorID: 'originalInstructorID',
  replacementInstructorID: 'replacementInstructorID',
  scheduledLessonID: 'scheduledLessonID',
  dateOfAbsence: 'dateOfAbsence',
  reason: 'reason',
  status: 'status',
  notes: 'notes',
  requestedBy: 'requestedBy',
  approvedBy: 'approvedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.PublishedScheduleScalarFieldEnum = {
  publishedScheduleID: 'publishedScheduleID',
  semesterID: 'semesterID',
  scheduleData: 'scheduleData',
  publishedAt: 'publishedAt',
  publishedBy: 'publishedBy',
  version: 'version',
  isActive: 'isActive',
  notes: 'notes'
};

exports.Prisma.SchedulingConstraintScalarFieldEnum = {
  constraintID: 'constraintID',
  name: 'name',
  description: 'description',
  constraintType: 'constraintType',
  ruleDefinition: 'ruleDefinition',
  weight: 'weight',
  isActive: 'isActive',
  semesterID: 'semesterID',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.GenerationSessionScalarFieldEnum = {
  sessionID: 'sessionID',
  semesterID: 'semesterID',
  status: 'status',
  manifestFilePath: 'manifestFilePath',
  jobID: 'jobID',
  generationStartedAt: 'generationStartedAt',
  generationCompletedAt: 'generationCompletedAt',
  totalLessons: 'totalLessons',
  scheduledLessons: 'scheduledLessons',
  unscheduledLessons: 'unscheduledLessons',
  qualityScore: 'qualityScore',
  constraintViolations: 'constraintViolations',
  errorLog: 'errorLog',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserScalarFieldEnum = {
  userID: 'userID',
  username: 'username',
  email: 'email',
  passwordHash: 'passwordHash',
  role: 'role',
  departmentID: 'departmentID',
  instructorID: 'instructorID',
  isActive: 'isActive',
  lastLogin: 'lastLogin',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.RoomType = exports.$Enums.RoomType = {
  LECTURE_HALL: 'LECTURE_HALL',
  CLASSROOM: 'CLASSROOM',
  LAB: 'LAB',
  TUTORIAL_ROOM: 'TUTORIAL_ROOM',
  COMPUTER_LAB: 'COMPUTER_LAB'
};

exports.GroupType = exports.$Enums.GroupType = {
  FULL_LEVEL: 'FULL_LEVEL',
  GROUP: 'GROUP',
  SUB_GROUP: 'SUB_GROUP'
};

exports.LessonType = exports.$Enums.LessonType = {
  LECTURE: 'LECTURE',
  LAB: 'LAB',
  TUTORIAL: 'TUTORIAL',
  COMPUTER_LAB: 'COMPUTER_LAB',
  SEMINAR: 'SEMINAR',
  WORKSHOP: 'WORKSHOP'
};

exports.DayOfWeek = exports.$Enums.DayOfWeek = {
  MONDAY: 'MONDAY',
  TUESDAY: 'TUESDAY',
  WEDNESDAY: 'WEDNESDAY',
  THURSDAY: 'THURSDAY',
  FRIDAY: 'FRIDAY',
  SATURDAY: 'SATURDAY',
  SUNDAY: 'SUNDAY'
};

exports.ScheduleStatus = exports.$Enums.ScheduleStatus = {
  SCHEDULED: 'SCHEDULED',
  LOCKED: 'LOCKED',
  CANCELLED: 'CANCELLED',
  MODIFIED: 'MODIFIED'
};

exports.SubstitutionStatus = exports.$Enums.SubstitutionStatus = {
  PENDING: 'PENDING',
  APPROVED: 'APPROVED',
  REJECTED: 'REJECTED',
  COMPLETED: 'COMPLETED'
};

exports.ConstraintType = exports.$Enums.ConstraintType = {
  HARD: 'HARD',
  SOFT: 'SOFT',
  ADVANCED: 'ADVANCED'
};

exports.GenerationStatus = exports.$Enums.GenerationStatus = {
  NOT_STARTED: 'NOT_STARTED',
  DATA_INPUT: 'DATA_INPUT',
  MANIFEST_GENERATED: 'MANIFEST_GENERATED',
  MANIFEST_VALIDATED: 'MANIFEST_VALIDATED',
  SCHEDULING_IN_PROGRESS: 'SCHEDULING_IN_PROGRESS',
  SCHEDULING_COMPLETED: 'SCHEDULING_COMPLETED',
  SCHEDULING_FAILED: 'SCHEDULING_FAILED',
  PUBLISHED: 'PUBLISHED'
};

exports.UserRole = exports.$Enums.UserRole = {
  ADMIN: 'ADMIN',
  SCHEDULER: 'SCHEDULER',
  DEPARTMENT_COORDINATOR: 'DEPARTMENT_COORDINATOR',
  INSTRUCTOR: 'INSTRUCTOR',
  STUDENT: 'STUDENT'
};

exports.Prisma.ModelName = {
  Semester: 'Semester',
  Department: 'Department',
  Room: 'Room',
  Instructor: 'Instructor',
  InstructorAvailability: 'InstructorAvailability',
  StudentLevel: 'StudentLevel',
  StudentGroup: 'StudentGroup',
  Course: 'Course',
  Lesson: 'Lesson',
  ScheduledLesson: 'ScheduledLesson',
  Substitution: 'Substitution',
  PublishedSchedule: 'PublishedSchedule',
  SchedulingConstraint: 'SchedulingConstraint',
  GenerationSession: 'GenerationSession',
  User: 'User'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
